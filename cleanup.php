<?php
/**
 * EcoGenix Cleanup Script
 * Удаление установочных файлов после успешной установки
 */

// Проверяем, что база данных установлена
$config_exists = file_exists(__DIR__ . '/config/database.php');
$schema_exists = file_exists(__DIR__ . '/database/schema.sql');

if (!$config_exists) {
    die('❌ Установка не завершена. Сначала запустите install.php');
}

// Файлы для удаления
$files_to_remove = [
    'install.php',
    'check_requirements.php',
    'cleanup.php',
    'INSTALL.md'
];

$removed_files = [];
$failed_files = [];

if ($_POST['confirm'] ?? false) {
    foreach ($files_to_remove as $file) {
        if (file_exists(__DIR__ . '/' . $file)) {
            if (unlink(__DIR__ . '/' . $file)) {
                $removed_files[] = $file;
            } else {
                $failed_files[] = $file;
            }
        }
    }
}
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Очистка установочных файлов - EcoGenix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #059669;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-warning {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            color: #92400e;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .file-list {
            background: #f9fafb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .file-list h3 {
            color: #374151;
            margin-bottom: 15px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .file-item:last-child {
            border-bottom: none;
        }
        
        .file-icon {
            margin-right: 10px;
            font-size: 1.2rem;
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
            margin: 10px 0;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .warning-box {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .warning-box h3 {
            color: #92400e;
            margin-bottom: 10px;
        }
        
        .warning-box p {
            color: #92400e;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🧹 EcoGenix</h1>
            <p>Очистка установочных файлов</p>
        </div>
        
        <?php if (!empty($removed_files) || !empty($failed_files)): ?>
            
            <?php if (!empty($removed_files)): ?>
                <div class="alert alert-success">
                    <strong>✅ Файлы успешно удалены:</strong><br>
                    <?php foreach ($removed_files as $file): ?>
                        • <?php echo htmlspecialchars($file); ?><br>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($failed_files)): ?>
                <div class="alert alert-error">
                    <strong>❌ Не удалось удалить файлы:</strong><br>
                    <?php foreach ($failed_files as $file): ?>
                        • <?php echo htmlspecialchars($file); ?><br>
                    <?php endforeach; ?>
                    <br><small>Удалите эти файлы вручную через FTP или файловый менеджер.</small>
                </div>
            <?php endif; ?>
            
            <a href="index.php" class="btn btn-primary">🏠 Перейти на главную страницу</a>
            
        <?php else: ?>
            
            <div class="alert alert-warning">
                <strong>⚠️ Внимание!</strong><br>
                Эта операция удалит все установочные файлы. После этого вы не сможете переустановить систему без повторной загрузки файлов.
            </div>
            
            <div class="file-list">
                <h3>📁 Файлы для удаления:</h3>
                <?php foreach ($files_to_remove as $file): ?>
                    <?php if (file_exists(__DIR__ . '/' . $file)): ?>
                        <div class="file-item">
                            <span class="file-icon">📄</span>
                            <span><?php echo htmlspecialchars($file); ?></span>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
            
            <div class="warning-box">
                <h3>🔒 Рекомендации по безопасности:</h3>
                <p>
                    • Убедитесь, что установка завершена успешно<br>
                    • Сделайте резервную копию базы данных<br>
                    • Проверьте работоспособность сайта<br>
                    • Удаление установочных файлов повышает безопасность
                </p>
            </div>
            
            <form method="POST">
                <input type="hidden" name="confirm" value="1">
                <button type="submit" class="btn btn-danger">🗑️ Удалить установочные файлы</button>
            </form>
            
            <a href="index.php" class="btn btn-secondary">❌ Отмена</a>
            
        <?php endif; ?>
    </div>
</body>
</html>
