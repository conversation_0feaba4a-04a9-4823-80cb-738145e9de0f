<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' . getSiteSetting('site_name', SITE_NAME) : getSiteSetting('site_name', SITE_NAME); ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? $page_description : getSiteSetting('site_description', 'Invest in sustainable cryptocurrency mining with EcoGenix - 100% renewable energy powered mining operations'); ?>">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Text Contrast Improvements -->
    <link rel="stylesheet" href="<?php echo getRelativePath('assets/css/text-contrast.css'); ?>">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-green: #059669;
            --secondary-green: #10b981;
            --light-green: #34d399;
            --accent-green: #6ee7b7;
            --dark-green: #047857;
            --emerald-green: #065f46;
            --eco-mint: #a7f3d0;
            --forest-green: #064e3b;
            --success-green: #10b981;
            --warning-orange: #f59e0b;
            --error-red: #ef4444;
            --neutral-gray: #6b7280;
            --light-gray: #f8fafc;
            --white: #ffffff;
            --black: #0f172a;
        }

        .bg-primary { background-color: var(--primary-green); }
        .bg-secondary { background-color: var(--secondary-green); }
        .bg-light-green { background-color: var(--light-green); }
        .bg-accent { background-color: var(--accent-green); }
        .bg-dark-green { background-color: var(--dark-green); }
        .bg-emerald { background-color: var(--emerald-green); }
        .bg-eco-mint { background-color: var(--eco-mint); }
        .bg-forest { background-color: var(--forest-green); }
        .text-primary { color: var(--primary-green); }
        .text-secondary { color: var(--secondary-green); }
        .text-light-green { color: var(--light-green); }
        .text-dark-green { color: var(--dark-green); }
        .text-emerald { color: var(--emerald-green); }
        .border-primary { border-color: var(--primary-green); }

        .gradient-primary {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 50%, var(--light-green) 100%);
        }

        .gradient-secondary {
            background: linear-gradient(135deg, var(--secondary-green) 0%, var(--light-green) 100%);
        }

        .gradient-eco {
            background: linear-gradient(135deg, var(--emerald-green) 0%, var(--primary-green) 50%, var(--accent-green) 100%);
        }

        .gradient-forest {
            background: linear-gradient(135deg, var(--forest-green) 0%, var(--dark-green) 100%);
        }

        .gradient-accent {
            background: linear-gradient(135deg, var(--light-green) 0%, var(--accent-green) 100%);
        }

        .hero-pattern {
            background-image:
                radial-gradient(circle at 20% 50%, rgba(5, 150, 105, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 80%, rgba(52, 211, 153, 0.1) 0%, transparent 50%);
        }

        .eco-pattern {
            background-image:
                radial-gradient(circle at 25% 25%, rgba(6, 95, 70, 0.15) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(4, 120, 87, 0.15) 0%, transparent 50%),
                linear-gradient(135deg, rgba(167, 243, 208, 0.05) 0%, transparent 100%);
        }

        .card-shadow {
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        .card-hover {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-eco {
            background: linear-gradient(135deg, var(--emerald-green) 0%, var(--primary-green) 50%, var(--secondary-green) 100%);
            box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-eco:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(5, 150, 105, 0.4);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(5, 150, 105, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, var(--secondary-green) 0%, var(--light-green) 100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            border: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
        }

        .btn-outline {
            background: transparent;
            border: 2px solid var(--primary-green);
            color: var(--primary-green);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 10px 22px;
            border-radius: 12px;
            font-weight: 600;
            text-decoration: none;
            cursor: pointer;
            min-height: 48px;
            line-height: 1.2;
        }

        .btn-outline:hover {
            background: var(--primary-green);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(5, 150, 105, 0.3);
        }

        .navbar-glass {
            backdrop-filter: blur(20px);
            background: rgba(6, 95, 70, 0.95);
            border-bottom: 1px solid rgba(5, 150, 105, 0.2);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .text-gradient {
            background: linear-gradient(135deg, var(--primary-green) 0%, var(--secondary-green) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .text-eco-gradient {
            background: linear-gradient(135deg, var(--emerald-green) 0%, var(--primary-green) 50%, var(--light-green) 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        /* Eco-friendly animations */
        @keyframes leafFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(5deg); }
        }

        @keyframes ecoGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(5, 150, 105, 0.3); }
            50% { box-shadow: 0 0 30px rgba(16, 185, 129, 0.5); }
        }

        @keyframes carbonOffset {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .eco-float {
            animation: leafFloat 3s ease-in-out infinite;
        }

        .eco-glow {
            animation: ecoGlow 2s ease-in-out infinite;
        }

        .carbon-fade-in {
            animation: carbonOffset 0.8s ease-out forwards;
        }

        /* Ensure text visibility on light backgrounds */
        .text-dark-contrast {
            color: var(--black) !important;
        }

        .text-medium-contrast {
            color: #374151 !important;
        }

        .text-light-contrast {
            color: #6b7280 !important;
        }

        /* Trust indicators visibility fix */
        .trust-indicators {
            color: #4b5563 !important;
        }

        /* Card text improvements */
        .card-text-primary {
            color: #1f2937 !important;
        }

        .card-text-secondary {
            color: #4b5563 !important;
        }

        /* Button improvements */
        .btn-outline.text-white {
            border-color: rgba(255, 255, 255, 0.8);
        }

        /* Footer text improvements */
        .footer-text-primary {
            color: #e5e7eb !important; /* gray-200 */
        }

        .footer-text-secondary {
            color: #d1d5db !important; /* gray-300 */
        }

        .footer-text-muted {
            color: #9ca3af !important; /* gray-400 */
        }

        /* Gradient background text improvements */
        .gradient-bg .text-gray-200 {
            color: #e5e7eb !important;
        }

        .gradient-bg .text-gray-300 {
            color: #d1d5db !important;
        }

        .gradient-bg .text-blue-200 {
            color: #e5e7eb !important;
        }

        /* Mobile menu enhancements */
        .mobile-menu-item {
            transition: all 0.3s ease;
        }

        .mobile-menu-item:hover {
            transform: translateX(5px);
        }

        #mobile-menu {
            backdrop-filter: blur(10px);
            background: #1e293b !important;
        }

        /* Ensure mobile navigation visibility */
        @media (max-width: 768px) {
            .navbar-glass {
                background: linear-gradient(135deg, #1e293b 0%, #1e40af 50%, #1e293b 100%) !important;
            }

            #mobile-menu-btn {
                color: white !important;
                border-color: rgba(255, 255, 255, 0.2) !important;
            }

            #mobile-menu .mobile-menu-item {
                color: white !important;
            }
        }

        .gradient-bg .text-blue-300 {
            color: #d1d5db !important;
        }

        .btn-outline.text-white:hover {
            border-color: white;
            background-color: white;
            color: var(--primary-blue) !important;
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }

        .animate-pulse-slow {
            animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .input-focus {
            transition: all 0.3s ease;
        }

        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }

        /* Responsive improvements */
        @media (max-width: 768px) {
            .hero-pattern {
                background-size: 50px 50px;
            }

            .animate-float {
                animation-duration: 4s;
            }

            .text-5xl {
                font-size: 2.5rem;
            }

            .text-7xl {
                font-size: 3rem;
            }
        }

        /* Scroll animations */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease-out;
        }

        .fade-in-up.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Loading spinner */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid var(--primary-blue);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-blue);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-blue);
        }

        /* Smooth scroll behavior */
        html {
            scroll-behavior: smooth;
        }

        /* Focus styles for accessibility */
        *:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        button:focus,
        a:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }

        /* Alert animations */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-out {
            animation: fadeOut 0.3s ease-in;
        }

        @keyframes fadeOut {
            from {
                opacity: 1;
                transform: translateY(0);
            }
            to {
                opacity: 0;
                transform: translateY(-10px);
            }
        }

        /* Alert styles */
        .alert-success {
            background: linear-gradient(135deg, #ecfdf5 0%, #f0fdf4 100%);
            border: 1px solid #bbf7d0;
            color: #166534;
        }

        .alert-error {
            background: linear-gradient(135deg, #fef2f2 0%, #fef2f2 100%);
            border: 1px solid #fecaca;
            color: #dc2626;
        }

        .alert-close-btn {
            transition: all 0.2s ease;
        }

        .alert-close-btn:hover {
            transform: scale(1.1);
        }
    </style>
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <!-- Mobile Styles -->
    <link rel="stylesheet" href="<?php echo getRelativePath('assets/css/mobile.css'); ?>">

    <style>
        body { font-family: 'Montserrat', sans-serif; }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 to-blue-50">
    <!-- Navigation -->
    <nav class="navbar-glass fixed w-full top-0 z-50" style="background: linear-gradient(135deg, #1e293b 0%, #1e40af 50%, #1e293b 100%); backdrop-filter: blur(10px);">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-20">
                <!-- Logo -->
                <div class="flex items-center">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="flex items-center space-x-3 group">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-anchor text-white text-xl"></i>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-xl font-bold text-white"><?php echo getSiteSetting('site_name', 'Poseidon'); ?></span>
                            <span class="text-sm text-gray-300 -mt-1">Investment Platform</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-1">
                    <a href="<?php echo getRelativePath('index.php'); ?>" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium flex items-center space-x-2">
                        <i class="fas fa-home text-blue-400"></i>
                        <span>Home</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium flex items-center space-x-2">
                        <i class="fas fa-chart-line text-green-400"></i>
                        <span>Investments</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium flex items-center space-x-2">
                        <i class="fas fa-newspaper text-purple-400"></i>
                        <span>Blog</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium flex items-center space-x-2">
                        <i class="fas fa-envelope text-orange-400"></i>
                        <span>Contact</span>
                    </a>
                    
                    <?php if (isLoggedIn()): ?>
                        <div class="flex items-center space-x-4">
                            <div class="relative group">
                                <button class="flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300">
                                    <div class="w-8 h-8 gradient-primary rounded-full flex items-center justify-center">
                                        <i class="fas fa-user text-white text-sm"></i>
                                    </div>
                                    <span class="font-medium"><?php echo $_SESSION['username']; ?></span>
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </button>
                                <div class="absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-xl border border-gray-100 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 transform group-hover:translate-y-1">
                                    <div class="p-2">
                                        <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 rounded-lg transition-colors">
                                            <i class="fas fa-tachometer-alt text-primary"></i>
                                            <span>Dashboard</span>
                                        </a>
                                        <a href="<?php echo getRelativePath('pages/verification.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 rounded-lg transition-colors">
                                            <i class="fas fa-shield-alt text-primary"></i>
                                            <span>Verification</span>
                                            <?php
                                            $user = getUserById(getCurrentUserId());
                                            $verification_status = $user['verification_status'] ?? 'unverified';
                                            if ($verification_status === 'pending'): ?>
                                                <span class="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded-full">Pending</span>
                                            <?php elseif ($verification_status === 'verified'): ?>
                                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">✓</span>
                                            <?php elseif ($verification_status === 'rejected'): ?>
                                                <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">!</span>
                                            <?php endif; ?>
                                        </a>
                                        <?php if (isAdmin()): ?>
                                            <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-gray-700 hover:bg-blue-50 rounded-lg transition-colors">
                                                <i class="fas fa-cog text-primary"></i>
                                                <span>Admin Panel</span>
                                            </a>
                                        <?php endif; ?>
                                        <hr class="my-2 border-gray-100">
                                        <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                                            <i class="fas fa-sign-out-alt"></i>
                                            <span>Logout</span>
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="flex items-center space-x-3">
                            <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="px-4 py-2 text-gray-300 hover:text-white transition-colors font-medium flex items-center space-x-2">
                                <i class="fas fa-sign-in-alt text-cyan-400"></i>
                                <span>Login</span>
                            </a>
                            <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-2 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center space-x-2">
                                <i class="fas fa-rocket"></i>
                                <span>Get Started</span>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="w-12 h-12 flex items-center justify-center text-white hover:text-blue-200 hover:bg-white hover:bg-opacity-10 rounded-lg transition-all duration-300 border-2 border-white border-opacity-20">
                        <i id="menu-icon" class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-slate-900 border-t border-blue-500 border-opacity-30 shadow-2xl">
            <div class="px-6 py-6 space-y-2">
                <a href="<?php echo getRelativePath('index.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-blue-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                    <i class="fas fa-home w-6 text-blue-400 text-lg"></i>
                    <span class="font-medium text-lg">Home</span>
                </a>
                <a href="<?php echo getRelativePath('pages/investments.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-green-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                    <i class="fas fa-chart-line w-6 text-green-400 text-lg"></i>
                    <span class="font-medium text-lg">Investments</span>
                </a>
                <a href="<?php echo getRelativePath('pages/blog.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-purple-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                    <i class="fas fa-newspaper w-6 text-purple-400 text-lg"></i>
                    <span class="font-medium text-lg">Blog</span>
                </a>
                <a href="<?php echo getRelativePath('pages/contact.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-orange-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                    <i class="fas fa-envelope w-6 text-orange-400 text-lg"></i>
                    <span class="font-medium text-lg">Contact</span>
                </a>

                <?php if (isLoggedIn()): ?>
                    <hr class="my-6 border-gray-600">
                    <div class="bg-blue-500 bg-opacity-20 rounded-xl p-4 mb-4 border border-blue-500 border-opacity-30">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-lg"></i>
                            </div>
                            <div>
                                <div class="font-bold text-white text-lg"><?php echo $_SESSION['username']; ?></div>
                                <div class="text-sm text-blue-200">Welcome back!</div>
                            </div>
                        </div>
                    </div>
                    <a href="<?php echo getRelativePath('pages/dashboard.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-cyan-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                        <i class="fas fa-tachometer-alt w-6 text-cyan-400 text-lg"></i>
                        <span class="font-medium text-lg">Dashboard</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/verification.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-blue-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                        <i class="fas fa-shield-alt w-6 text-blue-400 text-lg"></i>
                        <span class="font-medium text-lg">Verification</span>
                        <?php
                        $user = getUserById(getCurrentUserId());
                        $verification_status = $user['verification_status'] ?? 'unverified';
                        if ($verification_status === 'pending'): ?>
                            <span class="bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">Pending</span>
                        <?php elseif ($verification_status === 'verified'): ?>
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">✓</span>
                        <?php elseif ($verification_status === 'rejected'): ?>
                            <span class="bg-red-500 text-white text-xs px-2 py-1 rounded-full">!</span>
                        <?php endif; ?>
                    </a>
                    <?php if (isAdmin()): ?>
                        <a href="<?php echo getRelativePath('admin/index.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-yellow-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                            <i class="fas fa-cog w-6 text-yellow-400 text-lg"></i>
                            <span class="font-medium text-lg">Admin Panel</span>
                        </a>
                    <?php endif; ?>
                    <a href="<?php echo getRelativePath('pages/logout.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-red-400 hover:text-red-300 hover:bg-red-500 hover:bg-opacity-20 rounded-xl transition-all duration-300 mobile-menu-item">
                        <i class="fas fa-sign-out-alt w-6 text-lg"></i>
                        <span class="font-medium text-lg">Logout</span>
                    </a>
                <?php else: ?>
                    <hr class="my-6 border-gray-600">
                    <a href="<?php echo getRelativePath('pages/login.php'); ?>" class="flex items-center space-x-4 py-4 px-4 text-white hover:text-cyan-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300 mobile-menu-item">
                        <i class="fas fa-sign-in-alt w-6 text-cyan-400 text-lg"></i>
                        <span class="font-medium text-lg">Login</span>
                    </a>
                    <a href="<?php echo getRelativePath('pages/register.php'); ?>" class="block mt-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-6 py-4 rounded-xl text-center font-bold transition-all duration-300 transform hover:scale-105 shadow-lg text-lg">
                        <i class="fas fa-rocket mr-2"></i>
                        Get Started Free
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="pt-16">
        
        <!-- Flash Messages -->
        <?php
        $success_message = getFlashMessage('success');
        $error_message = getFlashMessage('error');
        $info_message = getFlashMessage('info');
        ?>
        
        <?php if ($success_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-green-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-green-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-green-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-check text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Success!</div>
                        <div class="text-sm opacity-90"><?php echo $success_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-green-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-red-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-red-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-exclamation-triangle text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Error!</div>
                        <div class="text-sm opacity-90"><?php echo $error_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-red-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($info_message): ?>
            <div class="fixed top-24 right-4 z-50 bg-blue-500 text-white px-6 py-4 rounded-xl shadow-2xl border-l-4 border-blue-600 max-w-md" role="alert">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                        <i class="fas fa-info text-sm"></i>
                    </div>
                    <div>
                        <div class="font-bold text-sm">Info</div>
                        <div class="text-sm opacity-90"><?php echo $info_message; ?></div>
                    </div>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-blue-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        <?php endif; ?>

    <script>
        // Mobile menu toggle with enhanced animation
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');

            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-times');

                // Enhanced slide down animation
                mobileMenu.style.maxHeight = '0';
                mobileMenu.style.overflow = 'hidden';
                mobileMenu.style.transition = 'max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                setTimeout(() => {
                    mobileMenu.style.maxHeight = '600px';
                }, 10);
            } else {
                mobileMenu.style.maxHeight = '0';
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');

                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.style.maxHeight = '';
                    mobileMenu.style.overflow = '';
                    mobileMenu.style.transition = '';
                }, 400);
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');

            if (!mobileMenu.contains(event.target) && !mobileMenuBtn.contains(event.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenuBtn.click();
                }
            }
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                const mobileMenu = document.getElementById('mobile-menu');
                const menuIcon = document.getElementById('menu-icon');
                mobileMenu.classList.add('hidden');
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
            }
        });

        // Scroll animations
        function initScrollAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('visible');
                    }
                });
            }, observerOptions);

            // Add fade-in-up class to elements that should animate
            document.querySelectorAll('.card-shadow, .group').forEach(el => {
                el.classList.add('fade-in-up');
                observer.observe(el);
            });
        }

        // Navbar scroll effect
        function initNavbarScroll() {
            const navbar = document.querySelector('nav');
            let lastScrollY = window.scrollY;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;

                if (currentScrollY > 100) {
                    navbar.classList.add('shadow-lg');
                    navbar.style.backdropFilter = 'blur(20px)';
                } else {
                    navbar.classList.remove('shadow-lg');
                    navbar.style.backdropFilter = 'blur(10px)';
                }

                // Hide/show navbar on scroll
                if (currentScrollY > lastScrollY && currentScrollY > 200) {
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    navbar.style.transform = 'translateY(0)';
                }

                lastScrollY = currentScrollY;
            });
        }

        // Smooth scroll for anchor links
        function initSmoothScroll() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Initialize all animations and effects
        document.addEventListener('DOMContentLoaded', function() {
            initScrollAnimations();
            initNavbarScroll();
            initSmoothScroll();
        });
    </script>
