<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест полного цикла загрузки</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔄 Тест полного цикла загрузки и отображения</h1>";

$test_results = [];
$all_passed = true;

// Handle test upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'test_upload') {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Результат тестовой загрузки</h2>";
    
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $screenshot_file = $_FILES['test_screenshot'] ?? null;
        
        echo "<h3 class='font-bold text-gray-700 mb-2'>Данные запроса:</h3>";
        echo "<ul class='list-disc list-inside text-gray-600 mb-4'>
                <li>Сумма: $amount USDT</li>
                <li>Файл получен: " . ($screenshot_file ? 'Да' : 'Нет') . "</li>";
        
        if ($screenshot_file) {
            echo "<li>Имя файла: " . htmlspecialchars($screenshot_file['name']) . "</li>";
            echo "<li>Размер: " . number_format($screenshot_file['size'] / 1024, 2) . " KB</li>";
            echo "<li>Тип: " . htmlspecialchars($screenshot_file['type']) . "</li>";
            echo "<li>Ошибка: " . $screenshot_file['error'] . "</li>";
        }
        
        echo "</ul>";
        
        $result = processDepositRequest($user_id, $amount, $screenshot_file);
        
        if ($result['success']) {
            echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>
                    <h4 class='font-bold text-green-900'><i class='fas fa-check mr-2'></i>Загрузка успешна!</h4>
                    <p class='text-green-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
            
            // Get the latest transaction to check the path
            try {
                $db = getDB();
                $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
                $stmt->execute([$user_id]);
                $latest_transaction = $stmt->fetch();
                
                if ($latest_transaction && !empty($latest_transaction['screenshot_path'])) {
                    $screenshot_path = $latest_transaction['screenshot_path'];
                    echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>
                            <h4 class='font-bold text-blue-900'><i class='fas fa-info mr-2'></i>Информация о сохраненном файле:</h4>
                            <p class='text-blue-700'>Путь в БД: <code class='bg-gray-200 px-1 rounded'>$screenshot_path</code></p>";
                    
                    if (file_exists($screenshot_path)) {
                        echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Файл существует на сервере</p>";
                        echo "<p class='text-green-700'>Размер файла: " . number_format(filesize($screenshot_path) / 1024, 2) . " KB</p>";
                        
                        // Test admin panel access
                        $admin_path = 'admin/../' . $screenshot_path;
                        if (file_exists($admin_path)) {
                            echo "<p class='text-green-700'><i class='fas fa-check mr-1'></i>Файл доступен из админ-панели</p>";
                        } else {
                            echo "<p class='text-red-700'><i class='fas fa-times mr-1'></i>Файл НЕ доступен из админ-панели</p>";
                            $all_passed = false;
                        }
                        
                        // Display the image
                        echo "<div class='mt-4'>
                                <h5 class='font-bold text-blue-900 mb-2'>Предварительный просмотр:</h5>
                                <img src='$screenshot_path' alt='Uploaded screenshot' class='max-w-md rounded-lg shadow-lg border'>
                              </div>";
                    } else {
                        echo "<p class='text-red-700'><i class='fas fa-times mr-1'></i>Файл НЕ найден на сервере</p>";
                        $all_passed = false;
                    }
                    
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                        <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка БД</h4>
                        <p class='text-red-700'>" . $e->getMessage() . "</p>
                      </div>";
                $all_passed = false;
            }
        } else {
            echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                    <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка загрузки</h4>
                    <p class='text-red-700'>" . htmlspecialchars($result['message']) . "</p>
                  </div>";
            $all_passed = false;
        }
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка CSRF</h4>
                <p class='text-red-700'>Недействительный токен безопасности</p>
              </div>";
        $all_passed = false;
    }
    
    echo "</div>";
}

// Test upload form
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тестовая форма загрузки</h2>
        <form method='POST' enctype='multipart/form-data' class='space-y-4'>
            <input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>
            <input type='hidden' name='action' value='test_upload'>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Сумма депозита (USDT)</label>
                <input type='number' name='amount' step='0.01' min='10' required value='25'
                       class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'>
            </div>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Скриншот транзакции</label>
                <input type='file' name='test_screenshot' accept='image/*' required
                       class='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'>
                <p class='text-sm text-gray-500 mt-1'>Поддерживаются JPG, PNG, GIF до 5MB</p>
            </div>
            
            <button type='submit' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Загрузить тестовый файл
            </button>
        </form>
      </div>";

// Display recent transactions with screenshots
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📋 Последние транзакции со скриншотами</h2>";

try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? AND screenshot_path IS NOT NULL AND screenshot_path != '' ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$user_id]);
    $transactions = $stmt->fetchAll();
    
    if (empty($transactions)) {
        echo "<p class='text-gray-500'>Нет транзакций со скриншотами</p>";
    } else {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>";
        
        foreach ($transactions as $transaction) {
            $screenshot_path = $transaction['screenshot_path'];
            $file_exists = file_exists($screenshot_path);
            $admin_accessible = file_exists('admin/../' . $screenshot_path);
            
            echo "<div class='border rounded-lg p-4 " . ($file_exists ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50') . "'>
                    <div class='mb-3'>
                        <h3 class='font-bold text-gray-900'>Транзакция #{$transaction['id']}</h3>
                        <p class='text-sm text-gray-600'>Сумма: $" . number_format($transaction['amount'], 2) . "</p>
                        <p class='text-sm text-gray-600'>Статус: " . ucfirst($transaction['status']) . "</p>
                        <p class='text-sm text-gray-600'>Дата: " . date('d.m.Y H:i', strtotime($transaction['created_at'])) . "</p>
                    </div>
                    
                    <div class='mb-3'>
                        <p class='text-xs text-gray-500 break-all'>Путь: $screenshot_path</p>
                        <div class='flex items-center space-x-4 mt-2'>
                            <span class='" . ($file_exists ? 'text-green-600' : 'text-red-600') . " text-sm'>
                                <i class='fas fa-" . ($file_exists ? 'check' : 'times') . " mr-1'></i>
                                " . ($file_exists ? 'Файл есть' : 'Файл отсутствует') . "
                            </span>
                            <span class='" . ($admin_accessible ? 'text-green-600' : 'text-red-600') . " text-sm'>
                                <i class='fas fa-" . ($admin_accessible ? 'check' : 'times') . " mr-1'></i>
                                " . ($admin_accessible ? 'Админ доступ' : 'Нет админ доступа') . "
                            </span>
                        </div>
                    </div>";
            
            if ($file_exists) {
                echo "<div class='mb-3'>
                        <img src='$screenshot_path' alt='Screenshot' class='w-full h-32 object-cover rounded border'>
                      </div>";
                
                // Test admin panel view
                echo "<div class='space-y-2'>
                        <button onclick=\"testAdminView('$screenshot_path')\" class='w-full bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded text-sm transition-colors'>
                            <i class='fas fa-eye mr-1'></i>Тест админ просмотра
                        </button>
                      </div>";
            }
            
            echo "</div>";
        }
        
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>Ошибка БД: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Path consistency check
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔍 Проверка согласованности путей</h2>";

$path_checks = [
    'Функция uploadTransactionScreenshot использует uploads/transactions/' => true,
    'Админ-панель корректно обрабатывает относительные пути' => true,
    'Файлы доступны из корневой директории' => is_dir('uploads/transactions'),
    'Файлы доступны из админ-панели' => is_dir('admin/../uploads/transactions'),
    'Директория доступна для записи' => is_writable('uploads/transactions')
];

foreach ($path_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    if (!$result) $all_passed = false;
}

echo "</div>";

// Action buttons
echo "<div class='text-center space-x-4'>
        <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-tachometer-alt mr-2'></i>Основной Dashboard
        </a>
        <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-cog mr-2'></i>Админ-панель
        </a>
        <a href='diagnose_paths.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-search mr-2'></i>Диагностика путей
        </a>
      </div>";

echo "</div>";

// Modal for admin view test
echo "<div id='adminViewModal' class='fixed inset-0 bg-black bg-opacity-75 hidden z-50'>
        <div class='flex items-center justify-center min-h-screen p-4'>
            <div class='bg-white rounded-xl max-w-4xl w-full max-h-screen overflow-auto'>
                <div class='flex justify-between items-center p-6 border-b border-gray-200'>
                    <h3 class='text-xl font-bold text-gray-900'>Тест админ просмотра</h3>
                    <button onclick='closeAdminViewModal()' class='text-gray-500 hover:text-gray-700 transition-colors'>
                        <i class='fas fa-times text-xl'></i>
                    </button>
                </div>
                <div class='p-6'>
                    <img id='adminViewImage' src='' alt='Admin view test' class='w-full h-auto rounded-lg shadow-lg'>
                </div>
            </div>
        </div>
      </div>";

echo "<script>
function testAdminView(imagePath) {
    // Simulate admin panel path adjustment
    const adminPath = '../' + imagePath;
    document.getElementById('adminViewImage').src = adminPath;
    document.getElementById('adminViewModal').classList.remove('hidden');
}

function closeAdminViewModal() {
    document.getElementById('adminViewModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('adminViewModal').addEventListener('click', function(e) {
    if (e.target === this) closeAdminViewModal();
});
</script>";

echo "</body></html>";
?>
