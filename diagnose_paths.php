<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Диагностика путей файлов</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔍 Диагностика путей файлов</h1>";

// Check current working directory
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📁 Информация о директориях</h2>";

$current_dir = getcwd();
$script_dir = __DIR__;

echo "<div class='grid grid-cols-1 md:grid-cols-2 gap-6'>
        <div class='bg-blue-50 border border-blue-200 rounded-lg p-4'>
            <h3 class='font-bold text-blue-900 mb-2'>Текущая рабочая директория:</h3>
            <code class='text-blue-700 text-sm break-all'>$current_dir</code>
        </div>
        <div class='bg-green-50 border border-green-200 rounded-lg p-4'>
            <h3 class='font-bold text-green-900 mb-2'>Директория скрипта:</h3>
            <code class='text-green-700 text-sm break-all'>$script_dir</code>
        </div>
      </div>";

echo "</div>";

// Check upload directories
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📂 Проверка директорий загрузки</h2>";

$directories_to_check = [
    'uploads/transactions/',
    'admin/uploads/transactions/',
    '../uploads/transactions/',
    './uploads/transactions/'
];

foreach ($directories_to_check as $dir) {
    $full_path = realpath($dir);
    $exists = is_dir($dir);
    $writable = $exists ? is_writable($dir) : false;
    
    $color = $exists ? 'green' : 'red';
    $icon = $exists ? 'check' : 'times';
    
    echo "<div class='mb-4 p-4 border rounded-lg " . ($exists ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50') . "'>
            <div class='flex items-center mb-2'>
                <i class='fas fa-{$icon} text-{$color}-600 mr-2'></i>
                <span class='font-bold text-{$color}-900'>$dir</span>
            </div>
            <div class='text-sm text-gray-600 space-y-1'>
                <p>Существует: " . ($exists ? '✅ Да' : '❌ Нет') . "</p>";
    
    if ($exists) {
        echo "<p>Доступна для записи: " . ($writable ? '✅ Да' : '❌ Нет') . "</p>";
        echo "<p>Полный путь: <code class='bg-gray-200 px-1 rounded'>" . ($full_path ?: 'Не определен') . "</code></p>";
        
        // List files in directory
        $files = glob($dir . '*');
        if (!empty($files)) {
            echo "<p>Файлы (" . count($files) . "):</p>";
            echo "<ul class='list-disc list-inside ml-4'>";
            foreach (array_slice($files, 0, 5) as $file) {
                $filename = basename($file);
                $filesize = number_format(filesize($file) / 1024, 2);
                echo "<li><code>$filename</code> ({$filesize} KB)</li>";
            }
            if (count($files) > 5) {
                echo "<li>... и еще " . (count($files) - 5) . " файлов</li>";
            }
            echo "</ul>";
        } else {
            echo "<p>Файлы: Нет файлов</p>";
        }
    }
    
    echo "</div>
          </div>";
}

echo "</div>";

// Check database entries
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>💾 Проверка записей в базе данных</h2>";

try {
    $db = getDB();
    $stmt = $db->query("SELECT id, user_id, amount, screenshot_path, status, created_at FROM transactions WHERE screenshot_path IS NOT NULL AND screenshot_path != '' ORDER BY created_at DESC LIMIT 10");
    $transactions = $stmt->fetchAll();
    
    if (empty($transactions)) {
        echo "<p class='text-gray-500'>Нет транзакций со скриншотами в базе данных</p>";
    } else {
        echo "<div class='overflow-x-auto'>
                <table class='w-full border-collapse border border-gray-300'>
                    <thead>
                        <tr class='bg-gray-100'>
                            <th class='border border-gray-300 px-4 py-2 text-left'>ID</th>
                            <th class='border border-gray-300 px-4 py-2 text-left'>Путь к скриншоту</th>
                            <th class='border border-gray-300 px-4 py-2 text-left'>Файл существует?</th>
                            <th class='border border-gray-300 px-4 py-2 text-left'>Размер</th>
                            <th class='border border-gray-300 px-4 py-2 text-left'>Дата</th>
                        </tr>
                    </thead>
                    <tbody>";
        
        foreach ($transactions as $transaction) {
            $screenshot_path = $transaction['screenshot_path'];
            $file_exists = file_exists($screenshot_path);
            $file_size = $file_exists ? number_format(filesize($screenshot_path) / 1024, 2) . ' KB' : 'N/A';
            
            $row_color = $file_exists ? 'bg-green-50' : 'bg-red-50';
            $text_color = $file_exists ? 'text-green-700' : 'text-red-700';
            $icon = $file_exists ? 'check' : 'times';
            
            echo "<tr class='$row_color'>
                    <td class='border border-gray-300 px-4 py-2'>{$transaction['id']}</td>
                    <td class='border border-gray-300 px-4 py-2'>
                        <code class='text-sm break-all'>$screenshot_path</code>
                    </td>
                    <td class='border border-gray-300 px-4 py-2 $text_color'>
                        <i class='fas fa-$icon mr-1'></i>" . ($file_exists ? 'Да' : 'Нет') . "
                    </td>
                    <td class='border border-gray-300 px-4 py-2'>$file_size</td>
                    <td class='border border-gray-300 px-4 py-2'>{$transaction['created_at']}</td>
                  </tr>";
        }
        
        echo "</tbody>
              </table>
              </div>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>Ошибка базы данных: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test upload function path
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🧪 Тест функции загрузки</h2>";

if (function_exists('uploadTransactionScreenshot')) {
    // Get the upload directory from the function
    $reflection = new ReflectionFunction('uploadTransactionScreenshot');
    $function_code = file_get_contents($reflection->getFileName(), false, null, $reflection->getStartLine() - 1, $reflection->getEndLine() - $reflection->getStartLine() + 1);
    
    if (preg_match('/\$uploadDir\s*=\s*[\'"]([^\'"]+)[\'"]/', $function_code, $matches)) {
        $upload_dir_from_function = $matches[1];
        echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Функция uploadTransactionScreenshot использует директорию: <code class='bg-gray-200 px-1 rounded'>$upload_dir_from_function</code></p>";
        
        // Check if this directory exists
        if (is_dir($upload_dir_from_function)) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Директория существует</p>";
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Директория НЕ существует</p>";
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Не удалось определить директорию из функции</p>";
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
}

echo "</div>";

// Path resolution test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔗 Тест разрешения путей</h2>";

$test_paths = [
    'uploads/transactions/test.jpg',
    'admin/uploads/transactions/test.jpg',
    '../uploads/transactions/test.jpg',
    './uploads/transactions/test.jpg'
];

foreach ($test_paths as $path) {
    $absolute_path = realpath(dirname($path));
    $url_accessible = false;
    
    // Test if path would be web-accessible
    if (strpos($path, '../') === false && strpos($path, 'admin/') !== 0) {
        $url_accessible = true;
    }
    
    echo "<div class='mb-3 p-3 border rounded " . ($url_accessible ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50') . "'>
            <div class='font-bold text-gray-900'>$path</div>
            <div class='text-sm text-gray-600'>
                <p>Абсолютный путь: " . ($absolute_path ?: 'Не найден') . "</p>
                <p>Доступен через веб: " . ($url_accessible ? '✅ Да' : '⚠️ Возможны проблемы') . "</p>
            </div>
          </div>";
}

echo "</div>";

echo "<div class='text-center'>
        <a href='fix_path_consistency.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors mr-4'>
            <i class='fas fa-wrench mr-2'></i>Исправить пути
        </a>
        <a href='test_upload.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-upload mr-2'></i>Тест загрузки
        </a>
      </div>";

echo "</div></body></html>";
?>
