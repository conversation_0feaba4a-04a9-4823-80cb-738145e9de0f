<?php
// Create necessary directories for Poseidon platform

$directories = [
    'uploads',
    'uploads/transactions',
    'uploads/blog'
];

foreach ($directories as $dir) {
    if (!file_exists($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✓ Created directory: $dir\n";
        } else {
            echo "✗ Failed to create directory: $dir\n";
        }
    } else {
        echo "✓ Directory already exists: $dir\n";
    }
}

// Create .htaccess for security
$htaccessContent = "Options -Indexes\n<Files *.php>\nDeny from all\n</Files>";
$htaccessPath = 'uploads/.htaccess';

if (!file_exists($htaccessPath)) {
    if (file_put_contents($htaccessPath, $htaccessContent)) {
        echo "✓ Created security .htaccess file\n";
    } else {
        echo "✗ Failed to create .htaccess file\n";
    }
} else {
    echo "✓ Security .htaccess file already exists\n";
}

echo "\nDirectories setup complete!\n";
?>
