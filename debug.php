<?php
// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Poseidon Debug Page</h1>";

// Test 1: Basic PHP info
echo "<h2>1. PHP Environment</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "</p>";
echo "<p>PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "</p>";

// Test 2: File includes
echo "<h2>2. File Includes</h2>";
try {
    require_once 'config/config.php';
    echo "<p>✅ config.php loaded successfully</p>";
    echo "<p>SITE_URL: " . SITE_URL . "</p>";
} catch (Exception $e) {
    echo "<p>❌ Error loading config.php: " . $e->getMessage() . "</p>";
}

try {
    require_once 'config/database.php';
    echo "<p>✅ database.php loaded successfully</p>";
} catch (Exception $e) {
    echo "<p>❌ Error loading database.php: " . $e->getMessage() . "</p>";
}

// Test 3: Database connection
echo "<h2>3. Database Connection</h2>";
try {
    $db = getDB();
    if ($db) {
        echo "<p>✅ Database connection successful</p>";
        
        // Test query
        $stmt = $db->prepare("SHOW TABLES");
        $stmt->execute();
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<p>Tables in database: " . implode(', ', $tables) . "</p>";
        
        // Test investments table
        if (in_array('investments', $tables)) {
            $stmt = $db->prepare("SELECT COUNT(*) as count FROM investments");
            $stmt->execute();
            $count = $stmt->fetch()['count'];
            echo "<p>Investments count: " . $count . "</p>";
            
            if ($count > 0) {
                $stmt = $db->prepare("SELECT id, title FROM investments LIMIT 3");
                $stmt->execute();
                $investments = $stmt->fetchAll();
                echo "<p>Sample investments:</p><ul>";
                foreach ($investments as $inv) {
                    echo "<li>ID: " . $inv['id'] . " - " . htmlspecialchars($inv['title']) . "</li>";
                }
                echo "</ul>";
            }
        }
        
    } else {
        echo "<p>❌ Database connection failed - getDB() returned null</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database error: " . $e->getMessage() . "</p>";
}

// Test 4: Functions
echo "<h2>4. Functions Test</h2>";
try {
    require_once 'includes/functions.php';
    echo "<p>✅ functions.php loaded successfully</p>";
    
    // Test getAllInvestments
    $investments = getAllInvestments(true);
    echo "<p>getAllInvestments() returned " . count($investments) . " investments</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error with functions: " . $e->getMessage() . "</p>";
}

// Test 5: Path functions
echo "<h2>5. Path Functions</h2>";
echo "<p>Current script: " . $_SERVER['SCRIPT_NAME'] . "</p>";
echo "<p>Request URI: " . $_SERVER['REQUEST_URI'] . "</p>";
echo "<p>HTTP Host: " . $_SERVER['HTTP_HOST'] . "</p>";
echo "<p>getRelativePath('index.php'): " . getRelativePath('index.php') . "</p>";
echo "<p>getRelativePath('pages/login.php'): " . getRelativePath('pages/login.php') . "</p>";

// Test 6: Session
echo "<h2>6. Session</h2>";
echo "<p>Session status: " . session_status() . "</p>";
echo "<p>Session ID: " . session_id() . "</p>";
echo "<p>isLoggedIn(): " . (isLoggedIn() ? 'Yes' : 'No') . "</p>";

echo "<h2>Navigation Test</h2>";
echo '<p><a href="index.php">Go to Index</a></p>';
echo '<p><a href="pages/login.php">Go to Login</a></p>';
echo '<p><a href="test-db.php">Test Database</a></p>';
?>
