<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';
require_once '../includes/auth.php';

requireLogin();
$user_id = getCurrentUserId();
$user = getUserById($user_id);

$page_title = 'Withdraw Funds';
$page_description = 'Withdraw funds from your Poseidon investment account';

// Handle withdrawal request
$success_message = null;
$error_message = null;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'withdrawal') {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $amount = floatval($_POST['amount'] ?? 0);
        $wallet_address = sanitizeInput($_POST['wallet_address'] ?? '');
        
        // Debug information
        error_log("=== WITHDRAWAL PAGE DEBUG ===");
        error_log("Amount: $amount");
        error_log("Wallet Address: $wallet_address");
        error_log("User ID: $user_id");
        error_log("User Balance: " . ($user['balance'] ?? 0));

        $result = processWithdrawalRequest($user_id, $amount, $wallet_address);
        
        error_log("Result: " . print_r($result, true));
        error_log("=== END DEBUG ===");

        if ($result['success']) {
            $success_message = $result['message'];
        } else {
            $error_message = $result['message'];
        }
    } else {
        $error_message = 'Invalid security token. Please try again.';
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Success/Error Messages -->
        <?php if ($success_message): ?>
            <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-6 py-4 rounded-xl shadow-lg animate-fade-in">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3 text-xl"></i>
                    <div>
                        <h4 class="font-bold text-green-900">Success!</h4>
                        <p><?php echo htmlspecialchars($success_message); ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <?php if ($error_message): ?>
            <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-6 py-4 rounded-xl shadow-lg animate-fade-in">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>
                    <div>
                        <h4 class="font-bold text-red-900">Error</h4>
                        <p><?php echo htmlspecialchars($error_message); ?></p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-red-500 to-pink-600 rounded-2xl mb-4 shadow-lg">
                <i class="fas fa-minus text-white text-2xl"></i>
            </div>
            <h1 class="text-4xl font-bold text-gray-900 mb-2">Withdraw Funds</h1>
            <p class="text-xl text-gray-600">Request a withdrawal from your account</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Withdrawal Form -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-xl border border-gray-100 overflow-hidden">
                    <!-- Warning Section -->
                    <div class="bg-gradient-to-r from-yellow-500 to-orange-600 p-6 text-white">
                        <h3 class="text-xl font-bold mb-4 flex items-center">
                            <i class="fas fa-exclamation-triangle mr-3"></i>
                            Important Withdrawal Information
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Network</p>
                                <p class="text-yellow-100">TRC-20 USDT only</p>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Processing Time</p>
                                <p class="text-yellow-100">24-48 hours</p>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Minimum Amount</p>
                                <p class="text-yellow-100">$10 USDT</p>
                            </div>
                            <div class="bg-white bg-opacity-20 rounded-lg p-3">
                                <p class="font-semibold mb-1">Available Balance</p>
                                <p class="text-yellow-100 font-bold"><?php echo formatCurrency($user['balance'] ?? 0); ?></p>
                            </div>
                        </div>
                    </div>

                    <!-- Withdrawal Form -->
                    <div class="p-6">
                        <form method="POST" onsubmit="return validateWithdrawal()" class="space-y-6">
                            <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                            <input type="hidden" name="action" value="withdrawal">

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-dollar-sign text-red-500 mr-2"></i>
                                    Withdrawal Amount (USDT)
                                </label>
                                <input type="number" name="amount" step="0.01" min="10" max="<?php echo $user['balance'] ?? 0; ?>" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-lg"
                                       placeholder="Enter amount (minimum $10)">
                                <p class="text-sm text-gray-500 mt-2">
                                    Available balance: <span class="font-semibold text-green-600"><?php echo formatCurrency($user['balance'] ?? 0); ?></span>
                                </p>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-wallet text-red-500 mr-2"></i>
                                    TRC-20 USDT Wallet Address
                                </label>
                                <input type="text" name="wallet_address" id="walletAddressInput" required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 text-lg font-mono"
                                       placeholder="Enter your TRC-20 USDT address"
                                       pattern="^T[A-Za-z0-9]{33}$"
                                       title="Address must start with T and contain 34 characters"
                                       onchange="validateTRC20AddressInput()">
                                <div id="addressValidation" class="mt-2 text-sm hidden"></div>
                                <p class="text-sm text-gray-500 mt-2">
                                    Example: TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE
                                </p>
                            </div>

                            <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                                <h4 class="font-bold text-red-900 mb-2 flex items-center">
                                    <i class="fas fa-shield-alt mr-2"></i>
                                    Security Notice
                                </h4>
                                <ul class="text-sm text-red-800 space-y-1">
                                    <li>• Double-check your wallet address before submitting</li>
                                    <li>• Withdrawals cannot be reversed once processed</li>
                                    <li>• Only TRC-20 USDT addresses are supported</li>
                                    <li>• Processing may take 24-48 hours</li>
                                </ul>
                            </div>

                            <div class="flex space-x-4">
                                <a href="dashboard.php" class="flex-1 border-2 border-gray-300 text-gray-700 py-4 rounded-xl hover:bg-gray-50 transition-colors font-medium text-center">
                                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                                </a>
                                <button type="submit" class="flex-1 bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white py-4 rounded-xl font-medium transition-all duration-300 shadow-lg transform hover:scale-105">
                                    <i class="fas fa-paper-plane mr-2"></i>Submit Withdrawal Request
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Sidebar Information -->
            <div class="space-y-6">
                <!-- Instructions -->
                <div class="bg-white rounded-2xl shadow-xl border border-gray-100 p-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-4 flex items-center">
                        <i class="fas fa-info-circle text-red-500 mr-2"></i>
                        Withdrawal Process
                    </h3>
                    <ol class="space-y-3 text-sm text-gray-600">
                        <li class="flex items-start">
                            <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">1</span>
                            <span>Enter the amount you want to withdraw</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">2</span>
                            <span>Provide your TRC-20 USDT wallet address</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">3</span>
                            <span>Double-check all information carefully</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">4</span>
                            <span>Submit your withdrawal request</span>
                        </li>
                        <li class="flex items-start">
                            <span class="bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold mr-3 mt-0.5">5</span>
                            <span>Wait for admin approval and processing</span>
                        </li>
                    </ol>
                </div>

                <!-- Account Summary -->
                <div class="bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl p-6 text-white">
                    <h3 class="text-lg font-bold mb-4 flex items-center">
                        <i class="fas fa-chart-pie mr-2"></i>
                        Account Summary
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-blue-100">Available Balance:</span>
                            <span class="font-bold text-xl"><?php echo formatCurrency($user['balance'] ?? 0); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-100">Total Invested:</span>
                            <span class="font-semibold"><?php echo formatCurrency($user['total_invested'] ?? 0); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-blue-100">Total Profit:</span>
                            <span class="font-semibold text-green-300"><?php echo formatCurrency($user['total_profit'] ?? 0); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Support -->
                <div class="bg-gradient-to-br from-gray-700 to-gray-900 rounded-2xl p-6 text-white">
                    <h3 class="text-lg font-bold mb-4 flex items-center">
                        <i class="fas fa-headset mr-2"></i>
                        Need Help?
                    </h3>
                    <p class="text-gray-300 text-sm mb-4">
                        If you have any questions about withdrawals, our support team is here to help.
                    </p>
                    <a href="../contact.php" class="bg-white text-gray-900 px-4 py-2 rounded-lg font-semibold text-sm hover:bg-gray-100 transition-colors inline-block">
                        <i class="fas fa-envelope mr-2"></i>Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// TRC-20 address validation
function validateTRC20Address(address) {
    // TRC-20 addresses start with 'T' and are 34 characters long
    const trc20Pattern = /^T[A-Za-z0-9]{33}$/;
    return trc20Pattern.test(address);
}

function validateTRC20AddressInput() {
    const input = document.getElementById('walletAddressInput');
    const validation = document.getElementById('addressValidation');
    const address = input.value.trim();

    if (address.length === 0) {
        validation.classList.add('hidden');
        return;
    }

    validation.classList.remove('hidden');

    if (validateTRC20Address(address)) {
        validation.innerHTML = '<i class="fas fa-check text-green-600 mr-1"></i><span class="text-green-600">Valid TRC-20 address</span>';
        input.classList.remove('border-red-300');
        input.classList.add('border-green-300');
    } else {
        validation.innerHTML = '<i class="fas fa-times text-red-600 mr-1"></i><span class="text-red-600">Invalid TRC-20 address format</span>';
        input.classList.remove('border-green-300');
        input.classList.add('border-red-300');
    }
}

// Form validation
function validateWithdrawal() {
    const walletAddress = document.getElementById('walletAddressInput').value.trim();
    const amount = parseFloat(document.querySelector('input[name="amount"]').value);
    const maxBalance = <?php echo $user['balance'] ?? 0; ?>;

    // Validate wallet address
    if (!validateTRC20Address(walletAddress)) {
        alert('Please enter a valid TRC-20 USDT address');
        return false;
    }

    // Validate amount
    if (amount < 10) {
        alert('Minimum withdrawal amount is $10');
        return false;
    }

    if (amount > maxBalance) {
        alert('Withdrawal amount cannot exceed your available balance');
        return false;
    }

    // Final confirmation
    return confirm(
        `Are you sure you want to withdraw $${amount.toFixed(2)} USDT to address:\n\n${walletAddress}\n\nPlease double-check your wallet address as this cannot be reversed.`
    );
}

// Auto-hide success/error messages
setTimeout(function() {
    const alerts = document.querySelectorAll('.animate-fade-in');
    alerts.forEach(function(alert) {
        alert.style.opacity = '0';
        alert.style.transform = 'translateY(-20px)';
        setTimeout(function() {
            alert.remove();
        }, 300);
    });
}, 5000);
</script>

<?php include '../includes/footer.php'; ?>
