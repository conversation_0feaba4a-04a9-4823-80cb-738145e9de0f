-- EcoGenix Ecological Mining Platform Database Schema
CREATE DATABASE IF NOT EXISTS ecogenix_mining;
USE ecogenix_mining;

-- Users table
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL, -- Plain text for testing as requested
    balance DECIMAL(15,2) DEFAULT 0.00,
    is_admin BOOLEAN DEFAULT FALSE,
    role ENUM('user', 'admin') DEFAULT 'user',
    status ENUM('active', 'suspended') DEFAULT 'active',
    verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'verified', -- Disabled verification
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    reset_token VARCHAR(255) NULL,
    reset_expires DATETIME NULL
);

-- Eco Mining Investment Packages table
CREATE TABLE mining_packages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category ENUM('solar_mining', 'wind_mining', 'hydro_mining', 'geothermal_mining', 'green_hosting', 'carbon_neutral') NOT NULL DEFAULT 'solar_mining',
    min_amount DECIMAL(15,2) NOT NULL DEFAULT 50.00,
    max_amount DECIMAL(15,2) NULL,
    daily_rate DECIMAL(5,4) NOT NULL, -- Daily rate for mining
    duration_days INT NOT NULL DEFAULT 365,
    hash_rate VARCHAR(50), -- Mining hash rate (e.g., "100 TH/s")
    power_consumption VARCHAR(50), -- Power consumption (e.g., "3000W")
    energy_source VARCHAR(100), -- Energy source description
    carbon_offset DECIMAL(10,2), -- CO2 offset in tons
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User mining investments table
CREATE TABLE user_mining_investments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    package_id INT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    daily_rate DECIMAL(5,4) NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status ENUM('active', 'completed', 'cancelled') DEFAULT 'active',
    total_earned DECIMAL(15,2) DEFAULT 0.00,
    total_mined DECIMAL(15,8) DEFAULT 0.00000000, -- Total crypto mined
    last_profit_date DATE NULL,
    hash_rate VARCHAR(50),
    energy_consumed DECIMAL(10,2) DEFAULT 0.00, -- kWh consumed
    carbon_offset DECIMAL(10,2) DEFAULT 0.00, -- CO2 offset achieved
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (package_id) REFERENCES mining_packages(id) ON DELETE CASCADE
);

-- Transactions table (deposits and withdrawals)
CREATE TABLE transactions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    type ENUM('deposit', 'withdrawal') NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    transaction_hash VARCHAR(255) NULL,
    wallet_address VARCHAR(255) NULL,
    screenshot_path VARCHAR(500) NULL,
    admin_notes TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Daily mining profits table
CREATE TABLE daily_mining_profits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_investment_id INT NOT NULL,
    profit_amount DECIMAL(15,2) NOT NULL,
    crypto_mined DECIMAL(15,8) NOT NULL, -- Amount of crypto mined
    energy_used DECIMAL(10,2) NOT NULL, -- kWh used
    carbon_offset DECIMAL(10,2) NOT NULL, -- CO2 offset for the day
    profit_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_investment_id) REFERENCES user_mining_investments(id) ON DELETE CASCADE,
    UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
);

-- Eco Mining News table
CREATE TABLE eco_mining_news (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) UNIQUE NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    category ENUM('solar_mining', 'wind_mining', 'hydro_mining', 'green_tech', 'sustainability', 'market_news') DEFAULT 'green_tech',
    tags TEXT,
    meta_title VARCHAR(255),
    meta_description TEXT,
    image_url VARCHAR(500),
    is_published BOOLEAN DEFAULT TRUE,
    views_count INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Contact messages table
CREATE TABLE contact_messages (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    is_read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- User verifications table
CREATE TABLE user_verifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    birth_date DATE NOT NULL,
    passport_photo_path VARCHAR(500) NOT NULL,
    status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
    admin_notes TEXT NULL,
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP NULL,
    processed_by INT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert sample admin user (password: admin123 - plain text for testing)
INSERT INTO users (username, email, password, is_admin, role, status, balance) VALUES
('admin', '<EMAIL>', 'admin123', TRUE, 'admin', 'active', 0.00);

-- Insert sample eco mining packages
INSERT INTO mining_packages (title, description, category, min_amount, max_amount, daily_rate, duration_days, hash_rate, power_consumption, energy_source, carbon_offset, image_url) VALUES
('Solar Bitcoin Mining Rig', 'High-efficiency Bitcoin mining powered entirely by solar energy. Zero carbon footprint with premium ASIC miners and battery storage for 24/7 operation.', 'solar_mining', 100.00, 10000.00, 0.0045, 365, '100 TH/s', '3000W', '100% Solar Power with Battery Storage', 2.5, 'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=400'),
('Wind-Powered Ethereum Farm', 'Cutting-edge Ethereum mining facility powered by offshore wind turbines. Sustainable mining with guaranteed green energy certification.', 'wind_mining', 250.00, 25000.00, 0.0038, 365, '500 MH/s', '2500W', 'Offshore Wind Turbines', 3.2, 'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=400'),
('Hydro Mining Complex', 'Industrial-scale cryptocurrency mining powered by renewable hydroelectric energy. Located near major hydroelectric dams for maximum efficiency.', 'hydro_mining', 500.00, 50000.00, 0.0052, 365, '200 TH/s', '4000W', 'Hydroelectric Power Plant', 4.1, 'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=400'),
('Geothermal Mining Station', 'Revolutionary geothermal-powered mining operation utilizing natural earth energy. Consistent power supply with minimal environmental impact.', 'geothermal_mining', 750.00, 75000.00, 0.0048, 365, '150 TH/s', '3500W', 'Geothermal Energy Plant', 3.8, 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?w=400'),
('Green Cloud Mining', 'Distributed cloud mining network powered by renewable energy sources. Flexible mining with real-time monitoring and carbon offset tracking.', 'green_hosting', 50.00, 5000.00, 0.0035, 365, '50 TH/s', '1500W', 'Mixed Renewable Sources', 1.8, 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400'),
('Carbon Neutral Mining Pool', 'Premium mining pool with 100% carbon offset guarantee. Advanced mining hardware powered by certified renewable energy with tree planting program.', 'carbon_neutral', 1000.00, 100000.00, 0.0055, 365, '300 TH/s', '5000W', 'Certified Green Energy + Carbon Offsets', 5.5, 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?w=400');

-- Site settings table
CREATE TABLE IF NOT EXISTS site_settings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    setting_key VARCHAR(100) NOT NULL UNIQUE,
    setting_value TEXT,
    setting_type ENUM('text', 'number', 'boolean', 'json') DEFAULT 'text',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Balance changes log table
CREATE TABLE IF NOT EXISTS balance_changes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    admin_id INT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    operation ENUM('add', 'subtract') NOT NULL,
    notes TEXT,
    old_balance DECIMAL(10,2) NOT NULL,
    new_balance DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert quality eco mining news content
INSERT INTO eco_mining_news (title, slug, content, excerpt, category, tags, meta_title, meta_description, image_url) VALUES
('Solar-Powered Bitcoin Mining Reaches New Efficiency Record', 'solar-bitcoin-mining-efficiency-record',
'<p>A groundbreaking solar-powered Bitcoin mining facility in Nevada has achieved a new efficiency record, producing 95% of its energy needs from solar panels and battery storage systems. The facility, operated by GreenHash Technologies, demonstrates that sustainable cryptocurrency mining is not only possible but highly profitable.</p>

<p>The 50-megawatt solar installation covers 200 acres and includes advanced battery storage capable of maintaining operations during nighttime hours. This achievement marks a significant milestone in the evolution of eco-friendly cryptocurrency mining.</p>

<p>"We''ve proven that renewable energy and cryptocurrency mining can work together harmoniously," said Dr. Sarah Chen, Chief Technology Officer at GreenHash Technologies. "Our facility produces zero carbon emissions while maintaining competitive mining profitability."</p>

<p>The facility uses cutting-edge ASIC miners optimized for solar power fluctuations, with intelligent power management systems that adjust mining intensity based on available solar energy. During peak sunlight hours, the facility operates at maximum capacity, while battery storage ensures continuous operation.</p>

<p>This development comes as the cryptocurrency industry faces increasing scrutiny over its environmental impact. The success of solar-powered mining operations like this one provides a clear path forward for sustainable blockchain technology.</p>',
'Nevada solar mining facility achieves 95% renewable energy efficiency, setting new standards for sustainable cryptocurrency mining operations.',
'solar_mining', 'solar power, bitcoin mining, renewable energy, sustainability, green technology',
'Solar Bitcoin Mining Achieves Record Efficiency - EcoGenix News',
'Discover how solar-powered Bitcoin mining is revolutionizing sustainable cryptocurrency operations with record-breaking efficiency.',
'https://images.unsplash.com/photo-1509391366360-2e959784a276?w=800'),

('Wind Energy Powers Major Ethereum Mining Operation in Denmark', 'wind-ethereum-mining-denmark',
'<p>Denmark''s largest wind-powered Ethereum mining operation has officially launched, utilizing offshore wind turbines to power a state-of-the-art mining facility. The project represents a €50 million investment in sustainable blockchain technology.</p>

<p>Located on the Danish coast, the facility harnesses energy from 12 offshore wind turbines, each capable of generating 8 MW of power. The consistent North Sea winds provide reliable energy for the mining operation, which processes over 1,000 transactions per second.</p>

<p>The facility employs advanced GPU mining rigs specifically designed for wind power integration. Smart grid technology automatically adjusts mining operations based on wind conditions, maximizing both energy efficiency and mining profitability.</p>

<p>"Wind energy provides the perfect solution for large-scale cryptocurrency mining," explained Lars Andersen, Project Director. "The consistent power generation allows us to maintain stable mining operations while contributing to Denmark''s renewable energy goals."</p>

<p>The project has created 150 local jobs and is expected to contribute significantly to Denmark''s position as a leader in sustainable technology. The facility also includes a visitor center showcasing the intersection of renewable energy and blockchain technology.</p>',
'Denmark launches Europe''s largest wind-powered Ethereum mining facility, combining offshore wind energy with advanced blockchain technology.',
'wind_mining', 'wind energy, ethereum mining, offshore wind, Denmark, sustainable blockchain',
'Wind-Powered Ethereum Mining Launches in Denmark - EcoGenix',
'Learn about Denmark''s revolutionary wind-powered Ethereum mining facility and its impact on sustainable blockchain technology.',
'https://images.unsplash.com/photo-1466611653911-95081537e5b7?w=800'),

('Hydroelectric Mining Complex Achieves Carbon Negative Status', 'hydroelectric-mining-carbon-negative',
'<p>A revolutionary hydroelectric-powered cryptocurrency mining complex in Norway has achieved carbon negative status, actively removing more CO2 from the atmosphere than it produces. The facility combines renewable energy with innovative carbon capture technology.</p>

<p>The mining complex, built adjacent to a major hydroelectric dam, utilizes 100% renewable energy while operating advanced carbon capture systems. These systems remove CO2 from the atmosphere and convert it into useful products, creating a net positive environmental impact.</p>

<p>The facility processes multiple cryptocurrencies including Bitcoin, Ethereum, and emerging eco-friendly tokens. Its carbon capture system removes approximately 10,000 tons of CO2 annually while the mining operations produce less than 1,000 tons of indirect emissions.</p>

<p>"We''ve moved beyond carbon neutral to carbon negative mining," said Dr. Erik Haugen, Environmental Director. "This facility actually improves air quality while generating cryptocurrency profits."</p>

<p>The complex includes educational facilities and research laboratories focused on sustainable mining technologies. Partnerships with local universities have led to breakthrough innovations in energy-efficient mining hardware and carbon capture systems.</p>',
'Norwegian hydroelectric mining facility becomes world''s first carbon negative cryptocurrency operation through innovative carbon capture technology.',
'hydro_mining', 'hydroelectric power, carbon negative, carbon capture, Norway, sustainable mining',
'World''s First Carbon Negative Mining Facility - EcoGenix News',
'Discover how Norway''s hydroelectric mining complex achieved carbon negative status while maintaining profitable operations.',
'https://images.unsplash.com/photo-1581833971358-2c8b550f87b3?w=800');

-- Insert sample blog posts
INSERT INTO blog_posts (title, slug, content, excerpt, image_url, is_published) VALUES
('Luxury Real Estate Market Trends 2024', 'luxury-real-estate-trends-2024', 'The luxury real estate market continues to show strong performance in 2024, with premium properties in key locations maintaining their value and generating consistent returns for investors. Our analysis shows that oceanfront properties and urban penthouses are particularly attractive for rental income generation.', 'Analysis of current luxury real estate market trends and investment opportunities.', '/assets/images/blog-realestate.jpg', TRUE),
('Yacht Charter Industry Growth', 'yacht-charter-industry-growth', 'The yacht charter industry has experienced unprecedented growth over the past year, with demand for luxury yacht experiences reaching new heights. This presents excellent opportunities for investors looking to diversify their portfolio with high-yield maritime assets.', 'Exploring the booming yacht charter market and investment potential.', '/assets/images/blog-yacht.jpg', TRUE);
