<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Test results array
$test_results = [];

function runTest($test_name, $test_function) {
    global $test_results;
    try {
        $result = $test_function();
        $test_results[] = [
            'name' => $test_name,
            'status' => $result ? 'PASS' : 'FAIL',
            'message' => $result ? 'Тест пройден успешно' : 'Тест не пройден',
            'details' => ''
        ];
    } catch (Exception $e) {
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Ошибка теста: ' . $e->getMessage(),
            'details' => $e->getTraceAsString()
        ];
    }
}

// Test 1: Admin panel language consistency
runTest('Языковая консистентность админ-панели', function() {
    $admin_files = [
        'admin/blog-create.php',
        'admin/blog-edit.php', 
        'admin/settings.php',
        'admin/transactions.php'
    ];
    
    foreach ($admin_files as $file) {
        if (!file_exists($file)) {
            throw new Exception("Файл $file не найден");
        }
        
        $content = file_get_contents($file);
        // Check for common English phrases that should be in Russian
        $english_phrases = [
            'Title is required',
            'Content is required',
            'Blog post created successfully',
            'Failed to create',
            'Invalid security token',
            'Save Settings',
            'General Settings'
        ];
        
        foreach ($english_phrases as $phrase) {
            if (strpos($content, $phrase) !== false) {
                throw new Exception("Найден английский текст '$phrase' в файле $file");
            }
        }
    }
    return true;
});

// Test 2: Site settings system
runTest('Система настроек сайта', function() {
    $db = getDB();
    
    // Check if site_settings table exists
    $stmt = $db->prepare("SHOW TABLES LIKE 'site_settings'");
    $stmt->execute();
    if (!$stmt->fetch()) {
        throw new Exception("Таблица site_settings не существует");
    }
    
    // Test setting functions
    $test_key = 'test_setting_' . time();
    $test_value = 'test_value_123';
    
    if (!updateSiteSetting($test_key, $test_value)) {
        throw new Exception("Не удалось обновить настройку");
    }
    
    $retrieved_value = getSiteSetting($test_key);
    if ($retrieved_value !== $test_value) {
        throw new Exception("Значение настройки не совпадает");
    }
    
    // Clean up test setting
    $stmt = $db->prepare("DELETE FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$test_key]);
    
    return true;
});

// Test 3: Transaction screenshot management
runTest('Управление скриншотами транзакций', function() {
    if (!file_exists('admin/transactions.php')) {
        throw new Exception("Файл admin/transactions.php не найден");
    }

    $content = file_get_contents('admin/transactions.php');

    // Check for screenshot functionality
    $required_elements = [
        'viewScreenshot',
        'screenshotModal',
        'screenshot_path'
    ];

    foreach ($required_elements as $element) {
        if (strpos($content, $element) === false) {
            throw new Exception("Элемент '$element' не найден в системе управления транзакциями");
        }
    }

    // Check upload directory exists and is writable
    if (!is_dir('uploads/transactions')) {
        throw new Exception("Директория uploads/transactions не существует");
    }

    if (!is_writable('uploads/transactions')) {
        throw new Exception("Директория uploads/transactions недоступна для записи");
    }

    // Check upload functions exist
    if (!function_exists('uploadTransactionScreenshot')) {
        throw new Exception("Функция uploadTransactionScreenshot не найдена");
    }

    if (!function_exists('processDepositRequest')) {
        throw new Exception("Функция processDepositRequest не найдена");
    }

    return true;
});

// Test 4: Mobile navigation design
runTest('Дизайн мобильной навигации', function() {
    if (!file_exists('includes/header.php')) {
        throw new Exception("Файл includes/header.php не найден");
    }

    $content = file_get_contents('includes/header.php');

    // Check for mobile menu with dark background
    if (strpos($content, 'bg-slate-900') === false) {
        throw new Exception("Мобильное меню не имеет темного фона");
    }

    // Check for white text in mobile menu (improved contrast)
    if (strpos($content, 'text-white') === false) {
        throw new Exception("Мобильное меню не имеет белого текста для лучшего контраста");
    }

    return true;
});

// Test 5: Social media integration
runTest('Интеграция социальных сетей', function() {
    // Check if Telegram is included in settings
    if (!file_exists('admin/settings.php')) {
        throw new Exception("Файл admin/settings.php не найден");
    }
    
    $content = file_get_contents('admin/settings.php');
    
    if (strpos($content, 'telegram_url') === false) {
        throw new Exception("Telegram URL не найден в настройках");
    }
    
    // Check footer integration
    if (!file_exists('includes/footer.php')) {
        throw new Exception("Файл includes/footer.php не найден");
    }
    
    $footer_content = file_get_contents('includes/footer.php');
    
    if (strpos($footer_content, 'getSiteSetting') === false) {
        throw new Exception("Footer не использует динамические настройки");
    }
    
    return true;
});

// Test 6: Complete settings integration
runTest('Полная интеграция настроек', function() {
    $required_settings = [
        'site_name',
        'site_description', 
        'contact_email',
        'contact_phone',
        'company_address',
        'usdt_wallet_address',
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'linkedin_url',
        'telegram_url'
    ];
    
    foreach ($required_settings as $setting) {
        $value = getSiteSetting($setting, null);
        // Setting should exist (even if empty)
        if ($value === null && !updateSiteSetting($setting, '')) {
            throw new Exception("Не удалось создать настройку $setting");
        }
    }
    
    return true;
});

// Test 7: Upload directories
runTest('Директории загрузок', function() {
    $upload_dirs = [
        'uploads',
        'uploads/transactions',
        'uploads/blog'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("Не удалось создать директорию $dir");
            }
        }
        if (!is_writable($dir)) {
            throw new Exception("Директория $dir недоступна для записи");
        }
    }
    return true;
});

// Test 8: Admin navigation consistency
runTest('Консистентность навигации админ-панели', function() {
    if (!file_exists('admin/index.php')) {
        throw new Exception("Файл admin/index.php не найден");
    }
    
    $content = file_get_contents('admin/index.php');
    
    // Check for transactions link
    if (strpos($content, 'transactions.php') === false) {
        throw new Exception("Ссылка на управление транзакциями не найдена");
    }
    
    return true;
});

// Test 9: CSRF protection
runTest('CSRF защита', function() {
    $admin_files = [
        'admin/blog-create.php',
        'admin/blog-edit.php',
        'admin/settings.php',
        'admin/transactions.php'
    ];
    
    foreach ($admin_files as $file) {
        if (!file_exists($file)) {
            continue;
        }
        
        $content = file_get_contents($file);
        
        if (strpos($content, 'csrf_token') === false) {
            throw new Exception("CSRF защита не найдена в файле $file");
        }
    }
    
    return true;
});

// Test 10: Database integrity
runTest('Целостность базы данных', function() {
    $db = getDB();
    
    $required_tables = [
        'users',
        'investments', 
        'user_investments',
        'transactions',
        'daily_profits',
        'blog_posts',
        'contact_messages',
        'site_settings',
        'balance_changes'
    ];
    
    foreach ($required_tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if (!$stmt->fetch()) {
            throw new Exception("Таблица $table не существует");
        }
    }
    
    return true;
});

include 'includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                🚀 Тест комплексных улучшений Poseidon
            </h1>
            <p class="text-gray-600 text-lg">Проверка всех реализованных улучшений и исправлений</p>
        </div>

        <!-- Test Results Summary -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <?php
            $total_tests = count($test_results);
            $passed_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'PASS'; }));
            $failed_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'FAIL'; }));
            $error_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'ERROR'; }));
            ?>
            
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-vial text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего тестов</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_tests; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Успешно</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $passed_tests; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Ошибки</p>
                        <p class="text-2xl font-bold text-red-600"><?php echo $failed_tests + $error_tests; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Test Results -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Детальные результаты тестов</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php foreach ($test_results as $result): ?>
                        <div class="flex items-center justify-between p-4 rounded-lg border <?php 
                            echo $result['status'] === 'PASS' ? 'border-green-200 bg-green-50' : 
                                ($result['status'] === 'FAIL' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'); 
                        ?>">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center <?php 
                                    echo $result['status'] === 'PASS' ? 'bg-green-500' : 
                                        ($result['status'] === 'FAIL' ? 'bg-red-500' : 'bg-yellow-500'); 
                                ?>">
                                    <i class="fas <?php 
                                        echo $result['status'] === 'PASS' ? 'fa-check' : 
                                            ($result['status'] === 'FAIL' ? 'fa-times' : 'fa-exclamation'); 
                                    ?> text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900"><?php echo htmlspecialchars($result['name']); ?></h3>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($result['message']); ?></p>
                                    <?php if (!empty($result['details'])): ?>
                                        <details class="mt-2">
                                            <summary class="text-xs text-gray-500 cursor-pointer">Подробности</summary>
                                            <pre class="text-xs text-gray-600 mt-1 whitespace-pre-wrap"><?php echo htmlspecialchars($result['details']); ?></pre>
                                        </details>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <span class="px-3 py-1 rounded-full text-xs font-semibold <?php 
                                echo $result['status'] === 'PASS' ? 'bg-green-100 text-green-800' : 
                                    ($result['status'] === 'FAIL' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                            ?>">
                                <?php echo $result['status']; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Summary and Next Steps -->
        <?php if ($failed_tests > 0 || $error_tests > 0): ?>
            <div class="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
                <h3 class="text-lg font-bold text-red-900 mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Обнаружены проблемы
                </h3>
                <p class="text-red-700 mb-4">Некоторые улучшения требуют дополнительной настройки.</p>
            </div>
        <?php else: ?>
            <div class="mt-8 bg-green-50 border border-green-200 rounded-xl p-6">
                <h3 class="text-lg font-bold text-green-900 mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    Все улучшения успешно реализованы!
                </h3>
                <p class="text-green-700 mb-4">Поздравляем! Все комплексные улучшения Poseidon работают корректно.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    <a href="admin/index.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-cog mr-2"></i>Админ-панель
                    </a>
                    <a href="admin/settings.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-sliders-h mr-2"></i>Настройки сайта
                    </a>
                    <a href="admin/transactions.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-exchange-alt mr-2"></i>Транзакции
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
