<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Test results array
$test_results = [];

function runTest($test_name, $test_function) {
    global $test_results;
    try {
        $result = $test_function();
        $test_results[] = [
            'name' => $test_name,
            'status' => $result ? 'PASS' : 'FAIL',
            'message' => $result ? 'Тест пройден успешно' : 'Тест не пройден',
            'details' => ''
        ];
    } catch (Exception $e) {
        $test_results[] = [
            'name' => $test_name,
            'status' => 'ERROR',
            'message' => 'Ошибка теста: ' . $e->getMessage(),
            'details' => $e->getTraceAsString()
        ];
    }
}

// Test 1: Mobile menu design
runTest('Дизайн мобильного меню', function() {
    if (!file_exists('includes/header.php')) {
        throw new Exception("Файл includes/header.php не найден");
    }
    
    $content = file_get_contents('includes/header.php');
    
    // Check for dark background and white text in mobile menu
    if (strpos($content, 'bg-slate-900') === false) {
        throw new Exception("Мобильное меню не имеет темного фона");
    }
    
    if (strpos($content, 'text-white') === false) {
        throw new Exception("Мобильное меню не имеет белого текста");
    }
    
    return true;
});

// Test 2: Transaction photo upload system
runTest('Система загрузки фотографий транзакций', function() {
    if (!file_exists('pages/dashboard.php')) {
        throw new Exception("Файл pages/dashboard.php не найден");
    }
    
    $content = file_get_contents('pages/dashboard.php');
    
    // Check for file upload field
    if (strpos($content, 'transaction_screenshot') === false) {
        throw new Exception("Поле загрузки скриншота не найдено");
    }
    
    if (strpos($content, 'enctype="multipart/form-data"') === false) {
        throw new Exception("Форма не настроена для загрузки файлов");
    }
    
    // Check for upload directory
    if (!is_dir('uploads/transactions')) {
        if (!mkdir('uploads/transactions', 0755, true)) {
            throw new Exception("Не удалось создать директорию uploads/transactions");
        }
    }
    
    return true;
});

// Test 3: Admin settings reorganization
runTest('Реорганизация настроек админ-панели', function() {
    if (!file_exists('admin/settings.php')) {
        throw new Exception("Файл admin/settings.php не найден");
    }
    
    $content = file_get_contents('admin/settings.php');
    
    // Check that Telegram is in social media section
    $social_section_start = strpos($content, 'Ссылки на социальные сети');
    $telegram_position = strpos($content, 'telegram_url');
    
    if ($social_section_start === false) {
        throw new Exception("Секция социальных сетей не найдена");
    }
    
    if ($telegram_position === false) {
        throw new Exception("Поле Telegram URL не найдено");
    }
    
    // Check that Telegram comes after the social media section
    if ($telegram_position < $social_section_start) {
        throw new Exception("Telegram URL не находится в секции социальных сетей");
    }
    
    return true;
});

// Test 4: USDT wallet address consistency
runTest('Консистентность адреса USDT кошелька', function() {
    if (!file_exists('pages/dashboard.php')) {
        throw new Exception("Файл pages/dashboard.php не найден");
    }
    
    $content = file_get_contents('pages/dashboard.php');
    
    // Check that wallet address uses getSiteSetting
    if (strpos($content, 'getSiteSetting(\'usdt_wallet_address\'') === false) {
        throw new Exception("Адрес кошелька не использует динамические настройки");
    }
    
    return true;
});

// Test 5: Legal pages creation
runTest('Создание правовых страниц', function() {
    $legal_pages = [
        'pages/privacy-policy.php',
        'pages/terms-of-service.php',
        'pages/risk-disclosure.php',
        'pages/faq.php'
    ];
    
    foreach ($legal_pages as $page) {
        if (!file_exists($page)) {
            throw new Exception("Страница $page не найдена");
        }
        
        $content = file_get_contents($page);
        if (strlen($content) < 1000) {
            throw new Exception("Страница $page слишком короткая");
        }
    }
    
    return true;
});

// Test 6: Footer links to legal pages
runTest('Ссылки на правовые страницы в footer', function() {
    if (!file_exists('includes/footer.php')) {
        throw new Exception("Файл includes/footer.php не найден");
    }
    
    $content = file_get_contents('includes/footer.php');
    
    $required_links = [
        'pages/privacy-policy.php',
        'pages/terms-of-service.php',
        'pages/risk-disclosure.php',
        'pages/faq.php'
    ];
    
    foreach ($required_links as $link) {
        if (strpos($content, $link) === false) {
            throw new Exception("Ссылка на $link не найдена в footer");
        }
    }
    
    return true;
});

// Test 7: Blog.php functionality
runTest('Функциональность blog.php', function() {
    if (!file_exists('pages/blog.php')) {
        throw new Exception("Файл pages/blog.php не найден");
    }
    
    $content = file_get_contents('pages/blog.php');
    
    // Check for basic blog functionality
    if (strpos($content, 'getBlogPosts') === false) {
        throw new Exception("Функция getBlogPosts не найдена");
    }
    
    return true;
});

// Test 8: Dynamic site settings integration
runTest('Интеграция динамических настроек сайта', function() {
    $files_to_check = [
        'includes/header.php',
        'includes/footer.php',
        'pages/dashboard.php'
    ];
    
    foreach ($files_to_check as $file) {
        if (!file_exists($file)) {
            throw new Exception("Файл $file не найден");
        }
        
        $content = file_get_contents($file);
        
        if (strpos($content, 'getSiteSetting') === false) {
            throw new Exception("Файл $file не использует динамические настройки");
        }
    }
    
    return true;
});

// Test 9: Social media integration
runTest('Интеграция социальных сетей', function() {
    if (!file_exists('includes/footer.php')) {
        throw new Exception("Файл includes/footer.php не найден");
    }
    
    $content = file_get_contents('includes/footer.php');
    
    // Check for Telegram integration
    if (strpos($content, 'telegram_url') === false) {
        throw new Exception("Telegram не интегрирован в footer");
    }
    
    if (strpos($content, 'fab fa-telegram') === false) {
        throw new Exception("Иконка Telegram не найдена");
    }
    
    return true;
});

// Test 10: File upload functionality
runTest('Функциональность загрузки файлов', function() {
    // Check if upload functions exist
    if (!function_exists('uploadTransactionScreenshot')) {
        throw new Exception("Функция uploadTransactionScreenshot не найдена");
    }
    
    if (!function_exists('processDepositRequest')) {
        throw new Exception("Функция processDepositRequest не найдена");
    }
    
    // Check upload directories
    $upload_dirs = ['uploads', 'uploads/transactions', 'uploads/blog'];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            if (!mkdir($dir, 0755, true)) {
                throw new Exception("Не удалось создать директорию $dir");
            }
        }
        
        if (!is_writable($dir)) {
            throw new Exception("Директория $dir недоступна для записи");
        }
    }
    
    return true;
});

include 'includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">
                🔧 Тест финальных исправлений Poseidon
            </h1>
            <p class="text-gray-600 text-lg">Проверка всех специфических исправлений и улучшений</p>
        </div>

        <!-- Test Results Summary -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <?php
            $total_tests = count($test_results);
            $passed_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'PASS'; }));
            $failed_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'FAIL'; }));
            $error_tests = count(array_filter($test_results, function($r) { return $r['status'] === 'ERROR'; }));
            ?>
            
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-vial text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего тестов</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_tests; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-teal-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Успешно</p>
                        <p class="text-2xl font-bold text-green-600"><?php echo $passed_tests; ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Ошибки</p>
                        <p class="text-2xl font-bold text-red-600"><?php echo $failed_tests + $error_tests; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Detailed Test Results -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Детальные результаты тестов</h2>
            </div>
            <div class="p-6">
                <div class="space-y-4">
                    <?php foreach ($test_results as $result): ?>
                        <div class="flex items-center justify-between p-4 rounded-lg border <?php 
                            echo $result['status'] === 'PASS' ? 'border-green-200 bg-green-50' : 
                                ($result['status'] === 'FAIL' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'); 
                        ?>">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 rounded-full flex items-center justify-center <?php 
                                    echo $result['status'] === 'PASS' ? 'bg-green-500' : 
                                        ($result['status'] === 'FAIL' ? 'bg-red-500' : 'bg-yellow-500'); 
                                ?>">
                                    <i class="fas <?php 
                                        echo $result['status'] === 'PASS' ? 'fa-check' : 
                                            ($result['status'] === 'FAIL' ? 'fa-times' : 'fa-exclamation'); 
                                    ?> text-white text-sm"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900"><?php echo htmlspecialchars($result['name']); ?></h3>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($result['message']); ?></p>
                                    <?php if (!empty($result['details'])): ?>
                                        <details class="mt-2">
                                            <summary class="text-xs text-gray-500 cursor-pointer">Подробности</summary>
                                            <pre class="text-xs text-gray-600 mt-1 whitespace-pre-wrap"><?php echo htmlspecialchars($result['details']); ?></pre>
                                        </details>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <span class="px-3 py-1 rounded-full text-xs font-semibold <?php 
                                echo $result['status'] === 'PASS' ? 'bg-green-100 text-green-800' : 
                                    ($result['status'] === 'FAIL' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'); 
                            ?>">
                                <?php echo $result['status']; ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <!-- Summary -->
        <?php if ($failed_tests > 0 || $error_tests > 0): ?>
            <div class="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
                <h3 class="text-lg font-bold text-red-900 mb-4">
                    <i class="fas fa-exclamation-triangle mr-2"></i>
                    Обнаружены проблемы
                </h3>
                <p class="text-red-700">Некоторые исправления требуют дополнительной настройки.</p>
            </div>
        <?php else: ?>
            <div class="mt-8 bg-green-50 border border-green-200 rounded-xl p-6">
                <h3 class="text-lg font-bold text-green-900 mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    Все исправления успешно реализованы!
                </h3>
                <p class="text-green-700 mb-4">Поздравляем! Все специфические исправления работают корректно.</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-6">
                    <a href="pages/dashboard.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-tachometer-alt mr-2"></i>Дашборд
                    </a>
                    <a href="admin/settings.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-cog mr-2"></i>Настройки
                    </a>
                    <a href="pages/privacy-policy.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-shield-alt mr-2"></i>Политика
                    </a>
                    <a href="pages/faq.php" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg text-center transition-colors">
                        <i class="fas fa-question-circle mr-2"></i>FAQ
                    </a>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
