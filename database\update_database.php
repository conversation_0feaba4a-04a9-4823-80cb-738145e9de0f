<?php
require_once '../config/database.php';

try {
    $db = getDB();
    
    echo "Starting database update...\n";
    
    // Read and execute the SQL update script
    $sql = file_get_contents(__DIR__ . '/update_investments_table.sql');
    
    // Split by semicolon and execute each statement
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !str_starts_with($statement, '--')) {
            echo "Executing: " . substr($statement, 0, 50) . "...\n";
            $db->exec($statement);
        }
    }
    
    echo "Database update completed successfully!\n";
    
    // Verify the update
    $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
    $result = $stmt->fetch();
    echo "Total investments in database: " . $result['count'] . "\n";
    
    // Show sample data
    $stmt = $db->query("SELECT title, category, min_amount, monthly_rate FROM investments LIMIT 3");
    $investments = $stmt->fetchAll();
    
    echo "\nSample investments:\n";
    foreach ($investments as $inv) {
        echo "- {$inv['title']} ({$inv['category']}) - Min: ${$inv['min_amount']}, Rate: {$inv['monthly_rate']}%\n";
    }
    
} catch (Exception $e) {
    echo "Error updating database: " . $e->getMessage() . "\n";
    exit(1);
}
?>
