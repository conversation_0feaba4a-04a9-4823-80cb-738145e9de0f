<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тестирование исправлений изображений блога</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen'>
<div class='container mx-auto px-4 py-8'>";

echo "<div class='max-w-4xl mx-auto'>
        <div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h1 class='text-2xl font-bold text-gray-900 mb-4'>
                <i class='fas fa-image text-blue-500 mr-2'></i>
                Тестирование исправлений изображений блога
            </h1>
            <p class='text-gray-600'>Проверка исправлений ошибок 'Undefined array key image_url' и реализации fallback изображений</p>
        </div>";

try {
    // Test 1: Check if blog image functions exist
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 1: Функции изображений блога</h2>";
    
    $blog_functions = ['getBlogPostImage', 'getEscapedBlogImage'];
    foreach ($blog_functions as $func) {
        if (function_exists($func)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция $func существует</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция $func не найдена</p>";
        }
    }
    
    echo "</div>";
    
    // Test 2: Test functions with different post scenarios
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 2: Тестирование функций с разными сценариями</h2>";
    
    // Test case 1: Post with image_url
    $post_with_image = [
        'title' => 'Test Post with Image',
        'image_url' => 'https://example.com/test-image.jpg'
    ];
    
    $result1 = getBlogPostImage($post_with_image);
    if ($result1 === 'https://example.com/test-image.jpg') {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Пост с изображением: корректно возвращает URL изображения</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Пост с изображением: неверный результат</p>";
    }
    
    // Test case 2: Post without image_url key
    $post_without_image = [
        'title' => 'Test Post without Image'
    ];
    
    $result2 = getBlogPostImage($post_without_image);
    $expected_default = 'https://lim-english.com/uploads/images/all/img_articles_new/news_in_english.jpg';
    if ($result2 === $expected_default) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Пост без изображения: корректно возвращает fallback изображение</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Пост без изображения: неверный fallback</p>";
    }
    
    // Test case 3: Post with empty image_url
    $post_empty_image = [
        'title' => 'Test Post with Empty Image',
        'image_url' => ''
    ];
    
    $result3 = getBlogPostImage($post_empty_image);
    if ($result3 === $expected_default) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Пост с пустым изображением: корректно возвращает fallback изображение</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Пост с пустым изображением: неверный fallback</p>";
    }
    
    // Test case 4: Test escaped function
    $escaped_result = getEscapedBlogImage($post_with_image);
    if ($escaped_result === htmlspecialchars('https://example.com/test-image.jpg')) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Экранирование HTML: работает корректно</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Экранирование HTML: не работает</p>";
    }
    
    echo "</div>";
    
    // Test 3: Check file modifications
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 3: Проверка изменений в файлах</h2>";
    
    $files_to_check = [
        'pages/blog.php' => [
            'getEscapedBlogImage($featured_post)' => 'Использование функции для featured post',
            'getEscapedBlogImage($post)' => 'Использование функции для обычных постов'
        ],
        'pages/blog-post.php' => [
            'getEscapedBlogImage($post)' => 'Использование функции для основного изображения',
            'getEscapedBlogImage($related_post)' => 'Использование функции для связанных постов'
        ]
    ];
    
    foreach ($files_to_check as $file => $checks) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            echo "<h3 class='font-semibold text-gray-800 mb-2'>$file:</h3>";
            
            foreach ($checks as $pattern => $description) {
                if (strpos($content, $pattern) !== false) {
                    echo "<p class='text-green-600 ml-4'><i class='fas fa-check mr-2'></i>$description: ✓</p>";
                } else {
                    echo "<p class='text-red-600 ml-4'><i class='fas fa-times mr-2'></i>$description: ✗</p>";
                }
            }
            
            // Check for old problematic patterns
            if (strpos($content, "\$post['image_url']") !== false || strpos($content, "\$featured_post['image_url']") !== false) {
                echo "<p class='text-yellow-600 ml-4'><i class='fas fa-exclamation-triangle mr-2'></i>Найдены прямые обращения к image_url (могут вызывать ошибки)</p>";
            } else {
                echo "<p class='text-green-600 ml-4'><i class='fas fa-check mr-2'></i>Прямые обращения к image_url удалены</p>";
            }
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл $file не найден</p>";
        }
        echo "<br>";
    }
    
    echo "</div>";
    
    // Test 4: Visual test with sample data
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 4: Визуальная проверка изображений</h2>";
    
    $sample_posts = [
        [
            'title' => 'Пост с изображением',
            'image_url' => 'https://via.placeholder.com/300x200/4F46E5/FFFFFF?text=Custom+Image'
        ],
        [
            'title' => 'Пост без изображения'
        ],
        [
            'title' => 'Пост с пустым изображением',
            'image_url' => ''
        ]
    ];
    
    echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";
    foreach ($sample_posts as $index => $post) {
        $image_url = getEscapedBlogImage($post);
        echo "<div class='border rounded-lg p-4'>
                <h3 class='font-semibold mb-2'>" . htmlspecialchars($post['title']) . "</h3>
                <img src='$image_url' alt='" . htmlspecialchars($post['title']) . "' class='w-full h-32 object-cover rounded mb-2'>
                <p class='text-xs text-gray-600'>URL: " . htmlspecialchars($image_url) . "</p>
              </div>";
    }
    echo "</div>";
    
    echo "</div>";
    
    // Test 5: Error prevention check
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 5: Проверка предотвращения ошибок</h2>";
    
    // Test with completely empty array
    $empty_post = [];
    try {
        $result = getBlogPostImage($empty_post);
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Пустой массив: обработан без ошибок (результат: " . htmlspecialchars($result) . ")</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Пустой массив: вызвал ошибку - " . $e->getMessage() . "</p>";
    }
    
    // Test with null
    try {
        $result = getBlogPostImage(null);
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>NULL значение: обработано без ошибок</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>NULL значение: вызвал ошибку - " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Тестирование завершено!</h2>
            <p class='text-green-700 mb-4'>Исправления изображений блога протестированы.</p>
            
            <div class='grid grid-cols-1 md:grid-cols-2 gap-4 mb-4'>
                <a href='pages/blog.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-newspaper mr-2'></i>Проверить блог
                </a>
                <a href='pages/blog-post.php?slug=test' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-file-alt mr-2'></i>Проверить пост блога
                </a>
            </div>
            
            <div class='bg-white rounded-lg p-4'>
                <h3 class='font-bold text-gray-900 mb-2'>Исправленные проблемы:</h3>
                <ul class='text-sm text-gray-700 space-y-1'>
                    <li>✅ Устранены ошибки 'Undefined array key image_url'</li>
                    <li>✅ Реализован единый fallback image для всех постов блога</li>
                    <li>✅ Добавлены безопасные функции getBlogPostImage() и getEscapedBlogImage()</li>
                    <li>✅ Обновлены pages/blog.php и pages/blog-post.php</li>
                    <li>✅ Добавлено правильное экранирование HTML</li>
                    <li>✅ Обеспечена консистентность между страницами блога</li>
                </ul>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка</h2>
            <p class='text-red-700'>Ошибка тестирования: " . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></body></html>";
?>
