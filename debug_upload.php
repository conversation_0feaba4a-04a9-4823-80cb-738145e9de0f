<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Отладка загрузки файлов</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-4xl mx-auto p-8'>
    <h1 class='text-3xl font-bold text-gray-900 mb-8'>🔍 Отладка загрузки файлов</h1>";

// Handle deposit request (same as dashboard.php)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'deposit') {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Результат обработки</h2>";
    
    echo "<h3 class='font-bold text-gray-700 mb-2'>POST данные:</h3>";
    echo "<pre class='bg-gray-100 p-3 rounded text-sm mb-4'>" . print_r($_POST, true) . "</pre>";
    
    echo "<h3 class='font-bold text-gray-700 mb-2'>FILES данные:</h3>";
    echo "<pre class='bg-gray-100 p-3 rounded text-sm mb-4'>" . print_r($_FILES, true) . "</pre>";
    
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>CSRF токен валиден</p>";
        
        $amount = floatval($_POST['amount'] ?? 0);
        $screenshot_file = $_FILES['transaction_screenshot'] ?? null;

        echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Сумма: $amount</p>";
        
        if ($screenshot_file && $screenshot_file['error'] === 0) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл получен без ошибок</p>";
            
            $result = processDepositRequest($user_id, $amount, $screenshot_file);
            
            echo "<h3 class='font-bold text-gray-700 mb-2'>Результат processDepositRequest:</h3>";
            echo "<pre class='bg-gray-100 p-3 rounded text-sm mb-4'>" . print_r($result, true) . "</pre>";
            
            if ($result['success']) {
                echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>
                        <h4 class='font-bold text-green-900'><i class='fas fa-check mr-2'></i>Успех!</h4>
                        <p class='text-green-700'>" . htmlspecialchars($result['message']) . "</p>
                      </div>";
            } else {
                echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                        <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка!</h4>
                        <p class='text-red-700'>" . htmlspecialchars($result['message']) . "</p>
                      </div>";
            }
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка файла: " . ($screenshot_file['error'] ?? 'файл не получен') . "</p>";
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>CSRF токен недействителен</p>";
    }
    
    echo "</div>";
}

// Display upload form (simplified version of dashboard.php)
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест формы депозита</h2>
        <form method='POST' enctype='multipart/form-data' class='space-y-4'>
            <input type='hidden' name='csrf_token' value='" . generateCSRFToken() . "'>
            <input type='hidden' name='action' value='deposit'>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Сумма депозита (USDT)</label>
                <input type='number' name='amount' step='0.01' min='10' required value='50'
                       class='w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500'>
            </div>
            
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Скриншот транзакции</label>
                <input type='file' name='transaction_screenshot' accept='image/*' required
                       class='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'>
            </div>
            
            <button type='submit' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Отправить депозит
            </button>
        </form>
      </div>";

// Check functions and directories
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка системы</h2>";

$checks = [
    'Функция generateCSRFToken' => function_exists('generateCSRFToken'),
    'Функция verifyCSRFToken' => function_exists('verifyCSRFToken'),
    'Функция uploadTransactionScreenshot' => function_exists('uploadTransactionScreenshot'),
    'Функция processDepositRequest' => function_exists('processDepositRequest'),
    'Директория uploads/transactions' => is_dir('uploads/transactions'),
    'Директория доступна для записи' => is_writable('uploads/transactions')
];

foreach ($checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
}

echo "</div>";

// Show recent transactions
echo "<div class='bg-white rounded-lg shadow-lg p-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Последние транзакции</h2>";

try {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 5");
    $stmt->execute([$user_id]);
    $transactions = $stmt->fetchAll();
    
    if (empty($transactions)) {
        echo "<p class='text-gray-500'>Нет транзакций</p>";
    } else {
        echo "<div class='space-y-3'>";
        foreach ($transactions as $transaction) {
            echo "<div class='border rounded-lg p-4'>
                    <div class='flex justify-between items-start'>
                        <div>
                            <p class='font-medium'>" . ucfirst($transaction['type']) . "</p>
                            <p class='text-sm text-gray-500'>" . $transaction['created_at'] . "</p>
                            <p class='text-sm text-gray-600'>Статус: " . $transaction['status'] . "</p>
                        </div>
                        <div class='text-right'>
                            <p class='font-bold'>$" . number_format($transaction['amount'], 2) . "</p>";
            
            if (!empty($transaction['screenshot_path'])) {
                echo "<p class='text-green-600 text-sm'><i class='fas fa-image mr-1'></i>Скриншот есть</p>";
                echo "<img src='" . htmlspecialchars($transaction['screenshot_path']) . "' alt='Screenshot' class='mt-2 w-20 h-20 object-cover rounded'>";
            } else {
                echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Нет скриншота</p>";
            }
            
            echo "</div>
                    </div>
                  </div>";
        }
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'>Ошибка БД: " . $e->getMessage() . "</p>";
}

echo "</div>";

echo "<div class='mt-8 text-center'>
        <a href='pages/dashboard.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors mr-4'>
            <i class='fas fa-tachometer-alt mr-2'></i>Основной Dashboard
        </a>
        <a href='test_upload.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-vial mr-2'></i>Тест загрузки
        </a>
      </div>";

echo "</div></body></html>";
?>
