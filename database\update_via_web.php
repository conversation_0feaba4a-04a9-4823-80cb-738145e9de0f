<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Update - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-900 mb-6">Database Update</h1>
            
            <?php
            if (isset($_POST['update_db'])) {
                require_once '../config/database.php';
                
                try {
                    $db = getDB();

                    echo '<div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">';
                    echo '<h3 class="font-semibold text-blue-900 mb-2">Starting database update...</h3>';

                    // Check if investments table exists and has data
                    try {
                        $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                        $result = $stmt->fetch();
                        echo '<p class="text-sm text-blue-800">Current investments count: ' . $result['count'] . '</p>';
                    } catch (Exception $e) {
                        echo '<p class="text-sm text-blue-800">Investments table does not exist or is empty</p>';
                    }

                    // Step 1: Disable foreign key checks
                    $db->exec("SET FOREIGN_KEY_CHECKS = 0");
                    echo '<p class="text-sm text-blue-800">✓ Disabled foreign key checks</p>';

                    // Step 2: Drop dependent tables in correct order (child tables first)
                    $db->exec("DROP TABLE IF EXISTS daily_profits");
                    echo '<p class="text-sm text-blue-800">✓ Dropped daily_profits table</p>';

                    $db->exec("DROP TABLE IF EXISTS user_investments");
                    echo '<p class="text-sm text-blue-800">✓ Dropped user_investments table</p>';

                    $db->exec("DROP TABLE IF EXISTS investments");
                    echo '<p class="text-sm text-blue-800">✓ Dropped investments table</p>';

                    // Step 3: Recreate parent table first (investments)
                    $createInvestmentsSQL = "
                    CREATE TABLE investments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        description TEXT,
                        category ENUM('real_estate', 'stocks', 'crypto', 'bonds', 'commodities', 'forex', 'other') NOT NULL DEFAULT 'real_estate',
                        min_amount DECIMAL(15,2) NOT NULL DEFAULT 10.00,
                        max_amount DECIMAL(15,2) NULL,
                        monthly_rate DECIMAL(5,2) NOT NULL,
                        duration_months INT NULL,
                        location VARCHAR(255) DEFAULT 'Global',
                        capital_return BOOLEAN DEFAULT TRUE,
                        features TEXT NULL,
                        image_url VARCHAR(500),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )";

                    $db->exec($createInvestmentsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created investments table with correct structure</p>';

                    // Step 4: Update transactions table structure for file uploads
                    $db->exec("DROP TABLE IF EXISTS transactions");
                    echo '<p class="text-sm text-blue-800">✓ Dropped transactions table</p>';

                    $createTransactionsSQL = "
                    CREATE TABLE transactions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        type ENUM('deposit', 'withdrawal') NOT NULL,
                        amount DECIMAL(15,2) NOT NULL,
                        status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
                        screenshot_path VARCHAR(500) NULL,
                        wallet_address VARCHAR(255) NULL,
                        admin_notes TEXT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        processed_at TIMESTAMP NULL,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )";

                    $db->exec($createTransactionsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created transactions table with screenshot support</p>';

                    // Step 5: Recreate child tables with foreign key constraints
                    $createUserInvestmentsSQL = "
                    CREATE TABLE user_investments (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        investment_id INT NOT NULL,
                        amount DECIMAL(15,2) NOT NULL,
                        monthly_rate DECIMAL(5,2) NOT NULL,
                        start_date DATE NOT NULL,
                        end_date DATE NOT NULL,
                        total_profit DECIMAL(15,2) DEFAULT 0.00,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        FOREIGN KEY (investment_id) REFERENCES investments(id) ON DELETE CASCADE
                    )";

                    $db->exec($createUserInvestmentsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created user_investments table with foreign keys</p>';

                    $createDailyProfitsSQL = "
                    CREATE TABLE daily_profits (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_investment_id INT NOT NULL,
                        profit_amount DECIMAL(15,2) NOT NULL,
                        profit_date DATE NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_investment_id) REFERENCES user_investments(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_daily_profit (user_investment_id, profit_date)
                    )";

                    $db->exec($createDailyProfitsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created daily_profits table with foreign keys</p>';

                    // Step 6: Create blog posts table
                    $db->exec("DROP TABLE IF EXISTS blog_posts");
                    $createBlogPostsSQL = "
                    CREATE TABLE blog_posts (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        title VARCHAR(255) NOT NULL,
                        slug VARCHAR(255) UNIQUE NOT NULL,
                        content TEXT NOT NULL,
                        excerpt TEXT,
                        featured_image VARCHAR(500),
                        category VARCHAR(100) DEFAULT 'general',
                        tags TEXT,
                        meta_title VARCHAR(255),
                        meta_description TEXT,
                        is_published BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )";

                    $db->exec($createBlogPostsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created blog_posts table</p>';

                    // Step 7: Create site settings table
                    $db->exec("DROP TABLE IF EXISTS site_settings");
                    $createSettingsSQL = "
                    CREATE TABLE site_settings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        setting_key VARCHAR(100) UNIQUE NOT NULL,
                        setting_value TEXT,
                        setting_type ENUM('text', 'textarea', 'boolean', 'email', 'url') DEFAULT 'text',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                    )";

                    $db->exec($createSettingsSQL);
                    echo '<p class="text-sm text-blue-800">✓ Created site_settings table</p>';

                    // Step 8: Insert default settings
                    $defaultSettings = [
                        ['site_name', 'Poseidon Investment Platform', 'text'],
                        ['site_description', 'Luxury Investment Platform for Real Estate and Yachts', 'textarea'],
                        ['contact_email', '<EMAIL>', 'email'],
                        ['contact_phone', '******-0123', 'text'],
                        ['company_address', '123 Ocean Drive, Miami, FL 33139', 'textarea'],
                        ['usdt_wallet_address', 'TRC20WalletAddressHere', 'text'],
                        ['facebook_url', 'https://facebook.com/poseidon', 'url'],
                        ['twitter_url', 'https://twitter.com/poseidon', 'url'],
                        ['instagram_url', 'https://instagram.com/poseidon', 'url'],
                        ['linkedin_url', 'https://linkedin.com/company/poseidon', 'url'],
                        ['maintenance_mode', '0', 'boolean']
                    ];

                    foreach ($defaultSettings as $setting) {
                        $stmt = $db->prepare("INSERT INTO site_settings (setting_key, setting_value, setting_type) VALUES (?, ?, ?)");
                        $stmt->execute($setting);
                    }
                    echo '<p class="text-sm text-blue-800">✓ Inserted default site settings</p>';

                    // Step 9: Re-enable foreign key checks
                    $db->exec("SET FOREIGN_KEY_CHECKS = 1");
                    echo '<p class="text-sm text-blue-800">✓ Re-enabled foreign key checks</p>';

                    // Step 10: Create uploads directory
                    $uploadsDir = '../uploads';
                    $transactionDir = '../uploads/transactions';
                    $blogDir = '../uploads/blog';

                    if (!file_exists($uploadsDir)) {
                        mkdir($uploadsDir, 0755, true);
                        echo '<p class="text-sm text-blue-800">✓ Created uploads directory</p>';
                    }
                    if (!file_exists($transactionDir)) {
                        mkdir($transactionDir, 0755, true);
                        echo '<p class="text-sm text-blue-800">✓ Created transactions uploads directory</p>';
                    }
                    if (!file_exists($blogDir)) {
                        mkdir($blogDir, 0755, true);
                        echo '<p class="text-sm text-blue-800">✓ Created blog uploads directory</p>';
                    }

                    // Create .htaccess for security
                    $htaccessContent = "Options -Indexes\n<Files *.php>\nDeny from all\n</Files>";
                    file_put_contents($uploadsDir . '/.htaccess', $htaccessContent);
                    echo '<p class="text-sm text-blue-800">✓ Created security .htaccess file</p>';

                    // Step 11: Insert sample investment data
                    $insertSQL = "
                    INSERT INTO investments (title, description, category, min_amount, max_amount, monthly_rate, duration_months, location, capital_return, features, image_url, is_active) VALUES
                    ('Luxury Miami Penthouse', 'Premium oceanfront penthouse with stunning views and high-end amenities. Perfect for generating consistent rental income in the heart of Miami.', 'real_estate', 1000.00, 50000.00, 8.50, 24, 'Miami, Florida, USA', TRUE, 'Ocean view,Private balcony,24/7 concierge,Pool access,Gym facilities', 'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=400', TRUE),
                    ('Monaco Yacht Charter', 'Exclusive 120ft luxury yacht available for premium charter services. High-demand location with excellent returns.', 'other', 2500.00, 100000.00, 12.00, 18, 'Monaco, French Riviera', TRUE, 'Professional crew,Full insurance,Premium amenities,Global charter', 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=400', TRUE),
                    ('Dubai Marina Tower', 'High-rise luxury apartment in the heart of Dubai Marina with stunning views and modern amenities.', 'real_estate', 500.00, 25000.00, 7.25, 36, 'Dubai Marina, UAE', TRUE, 'Marina view,Modern furnishing,Shopping mall access,Metro connection', 'https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?w=400', TRUE),
                    ('Caribbean Sailing Yacht', 'Beautiful 80ft sailing yacht perfect for Caribbean charters. Year-round demand and experienced crew included.', 'other', 1500.00, 75000.00, 10.50, 24, 'Caribbean Islands', TRUE, 'Experienced crew,Island hopping,Water sports,Luxury cabins', 'https://images.unsplash.com/photo-1567899378494-47b22a2ae96a?w=400', TRUE),
                    ('Tech Stock Portfolio', 'Diversified portfolio of leading technology stocks with strong growth potential and dividend yields.', 'stocks', 100.00, 10000.00, 6.75, 12, 'Global Markets', FALSE, 'Diversified portfolio,Professional management,Quarterly reports,Low fees', 'https://images.unsplash.com/photo-1611974789855-9c2a0a7236a3?w=400', TRUE),
                    ('Cryptocurrency Fund', 'Managed cryptocurrency investment fund focusing on established coins with strong fundamentals.', 'crypto', 250.00, 15000.00, 15.00, 6, 'Digital Assets', FALSE, 'Top cryptocurrencies,Risk management,24/7 monitoring,High liquidity', 'https://images.unsplash.com/photo-1621761191319-c6fb62004040?w=400', FALSE)
                    ";

                    $db->exec($insertSQL);
                    echo '<p class="text-sm text-blue-800">✓ Sample investment data inserted successfully</p>';
                    echo '</div>';
                    
                    // Step 7: Verify the update and foreign key constraints
                    $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                    $result = $stmt->fetch();

                    // Test foreign key constraints
                    $stmt = $db->query("SHOW CREATE TABLE user_investments");
                    $userInvTable = $stmt->fetch();
                    $hasForeignKeys = strpos($userInvTable[1], 'FOREIGN KEY') !== false;

                    echo '<div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">';
                    echo '<h3 class="font-semibold text-green-900 mb-2">✅ Database update completed successfully!</h3>';
                    echo '<p class="text-sm text-green-800">Total investments in database: ' . $result['count'] . '</p>';
                    echo '<p class="text-sm text-green-800">Foreign key constraints: ' . ($hasForeignKeys ? 'Active ✓' : 'Missing ❌') . '</p>';
                    echo '<p class="text-sm text-green-800">All tables recreated with proper relationships</p>';
                    echo '</div>';
                    
                    // Show sample data
                    $stmt = $db->query("SELECT title, category, min_amount, monthly_rate, is_active FROM investments LIMIT 6");
                    $investments = $stmt->fetchAll();
                    
                    echo '<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">';
                    echo '<h3 class="font-semibold text-gray-900 mb-3">Sample investments:</h3>';
                    echo '<div class="space-y-2">';
                    foreach ($investments as $inv) {
                        $status = $inv['is_active'] ? 'Active' : 'Inactive';
                        $statusClass = $inv['is_active'] ? 'text-green-600' : 'text-red-600';
                        echo '<div class="flex justify-between items-center text-sm">';
                        echo '<span><strong>' . htmlspecialchars($inv['title']) . '</strong> (' . ucfirst($inv['category']) . ')</span>';
                        echo '<span>Min: $' . number_format($inv['min_amount'], 2) . ' | Rate: ' . $inv['monthly_rate'] . '% | <span class="' . $statusClass . '">' . $status . '</span></span>';
                        echo '</div>';
                    }
                    echo '</div>';
                    echo '</div>';
                    
                } catch (Exception $e) {
                    echo '<div class="bg-red-50 border border-red-200 rounded-lg p-4">';
                    echo '<h3 class="font-semibold text-red-900 mb-2">❌ Error updating database</h3>';
                    echo '<p class="text-sm text-red-800">' . htmlspecialchars($e->getMessage()) . '</p>';
                    echo '</div>';
                }
            } else {
                ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <h3 class="font-semibold text-yellow-900 mb-2">⚠️ Database Update Required</h3>
                    <p class="text-sm text-yellow-800 mb-3">
                        The investments table structure needs to be updated to match the admin panel expectations.
                        This will safely recreate the table structure while handling foreign key constraints.
                    </p>
                    <p class="text-sm text-yellow-800">
                        <strong>What this will do:</strong><br>
                        • Safely disable foreign key checks<br>
                        • Drop and recreate tables in correct order<br>
                        • Restore foreign key relationships<br>
                        • Add sample investment data<br>
                        • Fix admin panel display issues<br>
                        • Ensure referential integrity
                    </p>
                </div>
                
                <form method="POST" class="text-center">
                    <button type="submit" name="update_db" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                        Update Database
                    </button>
                </form>
                <?php
            }
            ?>
            
            <div class="mt-6 text-center">
                <a href="../admin/investments.php" class="text-blue-500 hover:text-blue-600 font-medium">
                    Go to Admin Panel →
                </a>
            </div>
        </div>
    </div>
</body>
</html>
