<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест исправления дублирования функций - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔧 Тест исправления дублирования функций</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            // Test 1: Check if processDepositRequest function exists and works
            try {
                if (function_exists('processDepositRequest')) {
                    $tests[] = ['name' => 'Функция processDepositRequest', 'status' => 'pass', 'message' => 'Функция существует и доступна'];
                } else {
                    $tests[] = ['name' => 'Функция processDepositRequest', 'status' => 'fail', 'message' => 'Функция не найдена'];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Функция processDepositRequest', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 2: Check if createTransaction function exists
            try {
                if (function_exists('createTransaction')) {
                    $tests[] = ['name' => 'Функция createTransaction', 'status' => 'pass', 'message' => 'Функция существует и доступна'];
                } else {
                    $tests[] = ['name' => 'Функция createTransaction', 'status' => 'fail', 'message' => 'Функция не найдена'];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Функция createTransaction', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 3: Check if processWithdrawalRequest function exists
            try {
                if (function_exists('processWithdrawalRequest')) {
                    $tests[] = ['name' => 'Функция processWithdrawalRequest', 'status' => 'pass', 'message' => 'Функция существует и доступна'];
                } else {
                    $tests[] = ['name' => 'Функция processWithdrawalRequest', 'status' => 'fail', 'message' => 'Функция не найдена'];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Функция processWithdrawalRequest', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 4: Check file upload functions
            $uploadFunctions = ['uploadTransactionScreenshot'];
            foreach ($uploadFunctions as $func) {
                if (function_exists($func)) {
                    $tests[] = ['name' => "Функция $func", 'status' => 'pass', 'message' => 'Функция загрузки файлов доступна'];
                } else {
                    $tests[] = ['name' => "Функция $func", 'status' => 'fail', 'message' => 'Функция загрузки файлов не найдена'];
                    $allPassed = false;
                }
            }
            
            // Test 5: Check site settings functions
            $settingsFunctions = ['getSiteSetting', 'updateSiteSetting', 'getAllSiteSettings'];
            $missingSettingsFunctions = [];
            foreach ($settingsFunctions as $func) {
                if (!function_exists($func)) {
                    $missingSettingsFunctions[] = $func;
                }
            }
            
            if (empty($missingSettingsFunctions)) {
                $tests[] = ['name' => 'Функции настроек сайта', 'status' => 'pass', 'message' => 'Все функции настроек доступны'];
            } else {
                $tests[] = ['name' => 'Функции настроек сайта', 'status' => 'fail', 'message' => 'Отсутствуют: ' . implode(', ', $missingSettingsFunctions)];
                $allPassed = false;
            }
            
            // Test 6: Check database connection and new schema
            try {
                $db = getDB();
                
                // Check transactions table structure
                $stmt = $db->query("DESCRIBE transactions");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $requiredColumns = ['id', 'user_id', 'type', 'amount', 'screenshot_path', 'wallet_address', 'status'];
                $missingColumns = [];
                
                foreach ($requiredColumns as $col) {
                    if (!in_array($col, $columns)) {
                        $missingColumns[] = $col;
                    }
                }
                
                if (empty($missingColumns)) {
                    $tests[] = ['name' => 'Схема таблицы transactions', 'status' => 'pass', 'message' => 'Все необходимые поля присутствуют'];
                } else {
                    $tests[] = ['name' => 'Схема таблицы transactions', 'status' => 'fail', 'message' => 'Отсутствуют поля: ' . implode(', ', $missingColumns)];
                    $allPassed = false;
                }
                
            } catch (Exception $e) {
                $tests[] = ['name' => 'Схема таблицы transactions', 'status' => 'fail', 'message' => 'Ошибка проверки БД: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 7: Check for function redeclaration errors
            $errorReporting = error_reporting(E_ALL);
            ob_start();
            
            try {
                // Try to include functions.php again to see if there are redeclaration errors
                $functionsContent = file_get_contents('includes/functions.php');
                $functionCount = substr_count($functionsContent, 'function processDepositRequest');
                
                if ($functionCount === 1) {
                    $tests[] = ['name' => 'Дублирование функций', 'status' => 'pass', 'message' => 'Функция processDepositRequest объявлена только один раз'];
                } else {
                    $tests[] = ['name' => 'Дублирование функций', 'status' => 'fail', 'message' => "Функция processDepositRequest объявлена $functionCount раз"];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Дублирование функций', 'status' => 'fail', 'message' => 'Ошибка проверки: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            ob_end_clean();
            error_reporting($errorReporting);
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 'text-red-800';
                $icon = $test['status'] === 'pass' ? '✅' : '❌';
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Дублирование функций успешно исправлено!' : '⚠️ Обнаружены проблемы с функциями';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'Отлично! Все функции работают корректно, дублирование устранено. Fatal error исправлен.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы с функциями.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Function Information -->
            <div class="mb-8 p-6 bg-blue-50 border border-blue-200 rounded-lg">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">📋 Информация об исправлениях</h3>
                <div class="text-sm text-blue-800 space-y-2">
                    <p><strong>Проблема:</strong> Fatal error: Cannot redeclare processDepositRequest()</p>
                    <p><strong>Причина:</strong> Дублирование функции в includes/functions.php</p>
                    <p><strong>Решение:</strong></p>
                    <ul class="list-disc list-inside ml-4 space-y-1">
                        <li>Удалена старая версия processDepositRequest() (работала с transaction_hash)</li>
                        <li>Оставлена новая версия processDepositRequest() (работает со screenshot_file)</li>
                        <li>Обновлена функция createTransaction() для новой схемы БД</li>
                        <li>Исправлена функция processWithdrawalRequest() для работы с новой схемой</li>
                        <li>Убрано поле transaction_hash, добавлено поле screenshot_path</li>
                    </ul>
                </div>
            </div>
            
            <!-- Test Links -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="pages/dashboard.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-tachometer-alt mr-2"></i>Тест дашборда
                </a>
                <a href="admin/deposits.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-money-bill mr-2"></i>Тест депозитов
                </a>
                <a href="test_enhancements.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-rocket mr-2"></i>Тест улучшений
                </a>
            </div>
            
            <div class="mt-6 text-center">
                <a href="final_check.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-check-double mr-2"></i>Финальная проверка
                </a>
            </div>
        </div>
    </div>
</body>
</html>
