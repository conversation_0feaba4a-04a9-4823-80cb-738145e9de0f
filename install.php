<?php
/**
 * EcoGenix Installation Script
 * Автоматическая установка базы данных для платформы экологического майнинга
 */

// Настройки по умолчанию
$default_config = [
    'host' => 'localhost',
    'username' => 'root',
    'password' => '',
    'database' => 'ecogenix_mining',
    'charset' => 'utf8mb4'
];

$error_message = '';
$success_message = '';

// Обработка формы установки
if ($_POST) {
    $host = $_POST['host'] ?? $default_config['host'];
    $username = $_POST['username'] ?? $default_config['username'];
    $password = $_POST['password'] ?? $default_config['password'];
    $database = $_POST['database'] ?? $default_config['database'];
    
    try {
        // Подключение к MySQL без указания базы данных
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Создание базы данных если не существует
        $pdo->exec("CREATE DATABASE IF NOT EXISTS `$database` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
        $pdo->exec("USE `$database`");
        
        // Чтение SQL файла
        $sql_file = __DIR__ . '/database/schema.sql';
        if (!file_exists($sql_file)) {
            throw new Exception("Файл schema.sql не найден в папке database/");
        }
        
        $sql_content = file_get_contents($sql_file);
        if ($sql_content === false) {
            throw new Exception("Не удалось прочитать файл schema.sql");
        }
        
        // Разделение SQL на отдельные запросы
        $sql_statements = array_filter(
            array_map('trim', explode(';', $sql_content)),
            function($stmt) {
                return !empty($stmt) && !preg_match('/^\s*--/', $stmt);
            }
        );
        
        // Выполнение SQL запросов
        foreach ($sql_statements as $statement) {
            if (!empty(trim($statement))) {
                $pdo->exec($statement);
            }
        }
        
        // Обновление конфигурационного файла
        $config_file = __DIR__ . '/config/database.php';
        $config_content = "<?php
// EcoGenix Database Configuration
// Автоматически сгенерировано установщиком

return [
    'host' => '$host',
    'dbname' => '$database',
    'username' => '$username',
    'password' => '$password',
    'charset' => 'utf8mb4',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ]
];
?>";
        
        if (file_put_contents($config_file, $config_content) === false) {
            throw new Exception("Не удалось обновить файл конфигурации database.php");
        }
        
        $success_message = "✅ Установка завершена успешно!<br>
                          ✅ База данных '$database' создана<br>
                          ✅ Все таблицы созданы<br>
                          ✅ Тестовые данные загружены<br>
                          ✅ Конфигурация обновлена<br><br>
                          <strong>Теперь вы можете:</strong><br>
                          • <a href='index.php'>Перейти на главную страницу</a><br>
                          • <a href='pages/register.php'>Зарегистрировать аккаунт</a><br>
                          • <a href='admin/'>Войти в админ-панель</a>";
        
    } catch (Exception $e) {
        $error_message = "❌ Ошибка установки: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Установка EcoGenix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 500px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo h1 {
            color: #059669;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .logo p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            color: #374151;
            font-weight: 600;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 10px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #059669;
        }
        
        .btn {
            width: 100%;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        
        .info {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">
            <h1>🌱 EcoGenix</h1>
            <p>Установка платформы экологического майнинга</p>
        </div>
        
        <?php if ($success_message): ?>
            <div class="alert alert-success">
                <?php echo $success_message; ?>
            </div>
        <?php else: ?>
            
            <?php if ($error_message): ?>
                <div class="alert alert-error">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>
            
            <div class="info">
                <strong>📋 Требования:</strong><br>
                • PHP 7.4+ с поддержкой PDO MySQL<br>
                • MySQL 5.7+ или MariaDB 10.2+<br>
                • Права на создание базы данных<br><br>
                <strong>🔧 Что будет установлено:</strong><br>
                • База данных ecogenix_mining<br>
                • Все необходимые таблицы<br>
                • Тестовые данные (6 майнинг-пакетов, 3 новости)<br>
                • Обновленная конфигурация
            </div>
            
            <form method="POST">
                <div class="form-group">
                    <label for="host">Хост MySQL:</label>
                    <input type="text" id="host" name="host" value="<?php echo htmlspecialchars($_POST['host'] ?? $default_config['host']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="username">Имя пользователя:</label>
                    <input type="text" id="username" name="username" value="<?php echo htmlspecialchars($_POST['username'] ?? $default_config['username']); ?>" required>
                </div>
                
                <div class="form-group">
                    <label for="password">Пароль:</label>
                    <input type="password" id="password" name="password" value="<?php echo htmlspecialchars($_POST['password'] ?? $default_config['password']); ?>">
                </div>
                
                <div class="form-group">
                    <label for="database">Название базы данных:</label>
                    <input type="text" id="database" name="database" value="<?php echo htmlspecialchars($_POST['database'] ?? $default_config['database']); ?>" required>
                </div>
                
                <button type="submit" class="btn">🚀 Установить EcoGenix</button>
            </form>
            
        <?php endif; ?>
    </div>
</body>
</html>
