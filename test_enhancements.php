<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест улучшений - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🚀 Тест новых улучшений Poseidon</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            try {
                $db = getDB();
                
                // Test 1: Check new database tables
                $newTables = ['transactions', 'blog_posts', 'site_settings'];
                foreach ($newTables as $table) {
                    try {
                        $stmt = $db->query("SELECT COUNT(*) FROM $table");
                        $count = $stmt->fetchColumn();
                        $tests[] = ['name' => "Таблица $table", 'status' => 'pass', 'message' => "Существует, записей: $count"];
                    } catch (Exception $e) {
                        $tests[] = ['name' => "Таблица $table", 'status' => 'fail', 'message' => 'Не найдена: ' . $e->getMessage()];
                        $allPassed = false;
                    }
                }
                
                // Test 2: Check file upload functions
                if (function_exists('uploadTransactionScreenshot') && function_exists('processDepositRequest')) {
                    $tests[] = ['name' => 'Функции загрузки файлов', 'status' => 'pass', 'message' => 'Все функции загрузки доступны'];
                } else {
                    $tests[] = ['name' => 'Функции загрузки файлов', 'status' => 'fail', 'message' => 'Отсутствуют функции загрузки'];
                    $allPassed = false;
                }
                
                // Test 3: Check site settings functions
                if (function_exists('getSiteSetting') && function_exists('updateSiteSetting') && function_exists('getAllSiteSettings')) {
                    $tests[] = ['name' => 'Функции настроек', 'status' => 'pass', 'message' => 'Все функции настроек доступны'];
                } else {
                    $tests[] = ['name' => 'Функции настроек', 'status' => 'fail', 'message' => 'Отсутствуют функции настроек'];
                    $allPassed = false;
                }
                
                // Test 4: Check uploads directory
                $uploadDirs = ['uploads', 'uploads/transactions', 'uploads/blog'];
                $missingDirs = [];
                foreach ($uploadDirs as $dir) {
                    if (!file_exists($dir)) {
                        $missingDirs[] = $dir;
                    }
                }
                
                if (empty($missingDirs)) {
                    $tests[] = ['name' => 'Директории загрузок', 'status' => 'pass', 'message' => 'Все директории созданы'];
                } else {
                    $tests[] = ['name' => 'Директории загрузок', 'status' => 'fail', 'message' => 'Отсутствуют: ' . implode(', ', $missingDirs)];
                    $allPassed = false;
                }
                
                // Test 5: Check admin pages
                $adminPages = ['admin/blog.php', 'admin/settings.php'];
                $missingPages = [];
                foreach ($adminPages as $page) {
                    if (!file_exists($page)) {
                        $missingPages[] = $page;
                    }
                }
                
                if (empty($missingPages)) {
                    $tests[] = ['name' => 'Админ-страницы', 'status' => 'pass', 'message' => 'Все новые админ-страницы созданы'];
                } else {
                    $tests[] = ['name' => 'Админ-страницы', 'status' => 'fail', 'message' => 'Отсутствуют: ' . implode(', ', $missingPages)];
                    $allPassed = false;
                }
                
                // Test 6: Check site settings data
                try {
                    $settings = getAllSiteSettings();
                    $requiredSettings = ['site_name', 'contact_email', 'usdt_wallet_address'];
                    $missingSettings = [];
                    
                    foreach ($requiredSettings as $setting) {
                        if (!isset($settings[$setting])) {
                            $missingSettings[] = $setting;
                        }
                    }
                    
                    if (empty($missingSettings)) {
                        $tests[] = ['name' => 'Настройки сайта', 'status' => 'pass', 'message' => 'Все базовые настройки загружены'];
                    } else {
                        $tests[] = ['name' => 'Настройки сайта', 'status' => 'warning', 'message' => 'Отсутствуют: ' . implode(', ', $missingSettings)];
                    }
                } catch (Exception $e) {
                    $tests[] = ['name' => 'Настройки сайта', 'status' => 'fail', 'message' => 'Ошибка загрузки настроек: ' . $e->getMessage()];
                    $allPassed = false;
                }
                
            } catch (Exception $e) {
                $tests[] = ['name' => 'Подключение к БД', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 
                          ($test['status'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200');
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 
                            ($test['status'] === 'warning' ? 'text-yellow-800' : 'text-red-800');
                $icon = $test['status'] === 'pass' ? '✅' : 
                       ($test['status'] === 'warning' ? '⚠️' : '❌');
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Все улучшения успешно внедрены!' : '⚠️ Обнаружены проблемы с улучшениями';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'Отлично! Все новые функции работают корректно. Платформа готова к использованию.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы перед использованием новых функций.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Feature Testing Links -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🧪 Тестирование новых функций</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <a href="pages/dashboard.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-upload mr-2"></i>Тест загрузки файлов
                    </a>
                    <a href="admin/deposits.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-image mr-2"></i>Просмотр скриншотов
                    </a>
                    <a href="admin/blog.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-newspaper mr-2"></i>Управление блогом
                    </a>
                    <a href="admin/settings.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-cog mr-2"></i>Настройки сайта
                    </a>
                    <a href="index.php" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-mobile-alt mr-2"></i>Мобильная навигация
                    </a>
                    <a href="admin/index.php" class="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        <i class="fas fa-tachometer-alt mr-2"></i>Админ-панель
                    </a>
                </div>
            </div>
            
            <!-- Enhancement Summary -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-blue-900 mb-4">📋 Реализованные улучшения</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-800">
                    <div>
                        <h4 class="font-semibold mb-2">✅ Дашборд и депозиты:</h4>
                        <ul class="space-y-1 list-disc list-inside">
                            <li>Удален QR код из модального окна депозита</li>
                            <li>Удалено поле transaction hash</li>
                            <li>Добавлена загрузка скриншотов транзакций</li>
                            <li>Валидация файлов (JPG, PNG, GIF, до 5MB)</li>
                            <li>Безопасное хранение в uploads/transactions/</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">✅ Админ-панель:</h4>
                        <ul class="space-y-1 list-disc list-inside">
                            <li>Просмотр скриншотов транзакций</li>
                            <li>Lightbox для увеличения изображений</li>
                            <li>Управление блогом с CRUD операциями</li>
                            <li>Настройки сайта и социальных сетей</li>
                            <li>Управление USDT кошельком</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">✅ Интерфейс:</h4>
                        <ul class="space-y-1 list-disc list-inside">
                            <li>Исправлена мобильная навигация</li>
                            <li>Темные цвета для лучшей видимости</li>
                            <li>Английские сообщения в дашборде</li>
                            <li>Адаптивный дизайн для всех экранов</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">✅ База данных:</h4>
                        <ul class="space-y-1 list-disc list-inside">
                            <li>Таблица transactions для скриншотов</li>
                            <li>Таблица blog_posts для блога</li>
                            <li>Таблица site_settings для настроек</li>
                            <li>Безопасные внешние ключи</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="mt-6 text-center">
                <a href="final_check.php" class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white px-8 py-3 rounded-lg font-medium transition-all duration-300 transform hover:scale-105 shadow-lg">
                    <i class="fas fa-check-double mr-2"></i>Финальная проверка всех систем
                </a>
            </div>
        </div>
    </div>
</body>
</html>
