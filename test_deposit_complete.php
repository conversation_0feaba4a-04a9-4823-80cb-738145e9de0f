<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Полный тест системы депозитов - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🧪 Полный тест системы депозитов</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            // Test 1: Check functions exist
            $tests[] = [
                'name' => 'Функция uploadTransactionScreenshot существует',
                'result' => function_exists('uploadTransactionScreenshot'),
                'message' => function_exists('uploadTransactionScreenshot') ? 'Функция найдена' : 'Функция не найдена'
            ];
            
            $tests[] = [
                'name' => 'Функция processDepositRequest существует',
                'result' => function_exists('processDepositRequest'),
                'message' => function_exists('processDepositRequest') ? 'Функция найдена' : 'Функция не найдена'
            ];
            
            // Test 2: Check directory structure
            $uploadDir = __DIR__ . '/uploads/transactions/';
            $tests[] = [
                'name' => 'Директория uploads/transactions/ существует',
                'result' => file_exists($uploadDir),
                'message' => file_exists($uploadDir) ? "Директория найдена: $uploadDir" : "Директория не найдена: $uploadDir"
            ];
            
            $tests[] = [
                'name' => 'Директория доступна для записи',
                'result' => is_writable($uploadDir),
                'message' => is_writable($uploadDir) ? 'Права записи есть' : 'Нет прав записи'
            ];
            
            // Test 3: Check database table
            try {
                $db = getDB();
                $stmt = $db->query("DESCRIBE transactions");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                $hasScreenshotPath = in_array('screenshot_path', $columns);
                
                $tests[] = [
                    'name' => 'Поле screenshot_path в таблице transactions',
                    'result' => $hasScreenshotPath,
                    'message' => $hasScreenshotPath ? 'Поле существует' : 'Поле отсутствует'
                ];
            } catch (Exception $e) {
                $tests[] = [
                    'name' => 'Проверка таблицы transactions',
                    'result' => false,
                    'message' => 'Ошибка: ' . $e->getMessage()
                ];
            }
            
            // Test 4: Create test user if needed
            $testUserId = null;
            try {
                $db = getDB();
                $stmt = $db->prepare("SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1");
                $stmt->execute();
                $testUser = $stmt->fetch();
                
                if ($testUser) {
                    $testUserId = $testUser['id'];
                    $tests[] = [
                        'name' => 'Тестовый пользователь найден',
                        'result' => true,
                        'message' => "ID пользователя: $testUserId"
                    ];
                } else {
                    // Create test user
                    $stmt = $db->prepare("INSERT INTO users (username, email, password, balance) VALUES ('testuser', '<EMAIL>', ?, 0.00)");
                    if ($stmt->execute([password_hash('testpass', PASSWORD_DEFAULT)])) {
                        $testUserId = $db->lastInsertId();
                        $tests[] = [
                            'name' => 'Тестовый пользователь создан',
                            'result' => true,
                            'message' => "ID пользователя: $testUserId"
                        ];
                    } else {
                        $tests[] = [
                            'name' => 'Создание тестового пользователя',
                            'result' => false,
                            'message' => 'Не удалось создать пользователя'
                        ];
                    }
                }
            } catch (Exception $e) {
                $tests[] = [
                    'name' => 'Работа с тестовым пользователем',
                    'result' => false,
                    'message' => 'Ошибка: ' . $e->getMessage()
                ];
            }
            
            // Test 5: Test file upload function
            if ($testUserId) {
                // Create a test image (1x1 PNG)
                $testImageData = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
                $testFilePath = 'test_complete_upload.png';
                file_put_contents($testFilePath, $testImageData);
                
                $testFile = [
                    'name' => 'test_complete_upload.png',
                    'type' => 'image/png',
                    'size' => strlen($testImageData),
                    'tmp_name' => $testFilePath,
                    'error' => 0
                ];
                
                // Test upload function
                $uploadResult = uploadTransactionScreenshot($testFile);
                $tests[] = [
                    'name' => 'Тест функции uploadTransactionScreenshot',
                    'result' => $uploadResult['success'],
                    'message' => $uploadResult['success'] ? 'Файл загружен: ' . $uploadResult['filepath'] : 'Ошибка: ' . $uploadResult['message']
                ];
                
                // Test complete deposit process
                if ($uploadResult['success']) {
                    $depositResult = processDepositRequest($testUserId, 25.00, $testFile);
                    $tests[] = [
                        'name' => 'Тест функции processDepositRequest',
                        'result' => $depositResult['success'],
                        'message' => $depositResult['success'] ? 'Депозит создан: ' . $depositResult['message'] : 'Ошибка: ' . $depositResult['message']
                    ];
                    
                    // Check if transaction was created in database
                    if ($depositResult['success'] && isset($depositResult['transaction_id'])) {
                        try {
                            $stmt = $db->prepare("SELECT * FROM transactions WHERE id = ?");
                            $stmt->execute([$depositResult['transaction_id']]);
                            $transaction = $stmt->fetch();
                            
                            $tests[] = [
                                'name' => 'Транзакция сохранена в базе данных',
                                'result' => $transaction !== false,
                                'message' => $transaction ? "Транзакция ID: {$transaction['id']}, Файл: {$transaction['screenshot_path']}" : 'Транзакция не найдена'
                            ];
                            
                            // Check if file exists
                            if ($transaction && $transaction['screenshot_path']) {
                                $fileExists = file_exists($transaction['screenshot_path']);
                                $tests[] = [
                                    'name' => 'Файл скриншота существует на сервере',
                                    'result' => $fileExists,
                                    'message' => $fileExists ? 'Файл найден: ' . $transaction['screenshot_path'] : 'Файл не найден: ' . $transaction['screenshot_path']
                                ];
                            }
                        } catch (Exception $e) {
                            $tests[] = [
                                'name' => 'Проверка транзакции в БД',
                                'result' => false,
                                'message' => 'Ошибка: ' . $e->getMessage()
                            ];
                        }
                    }
                }
                
                // Clean up test file
                if (file_exists($testFilePath)) {
                    unlink($testFilePath);
                }
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                if (!$test['result']) $allPassed = false;
                
                $bgColor = $test['result'] ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
                $textColor = $test['result'] ? 'text-green-800' : 'text-red-800';
                $icon = $test['result'] ? '✅' : '❌';
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . ($test['result'] ? 'PASS' : 'FAIL') . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Все тесты пройдены!' : '⚠️ Обнаружены проблемы';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'Система депозитов работает корректно. Файлы загружаются и сохраняются в базе данных.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы для корректной работы системы депозитов.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Action buttons -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
                <a href="pages/deposit.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-plus mr-2"></i>Тест депозита
                </a>
                <a href="admin/deposits.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-eye mr-2"></i>Админ депозиты
                </a>
                <a href="fix_deposit_upload.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-wrench mr-2"></i>Исправления
                </a>
                <button onclick="location.reload()" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    <i class="fas fa-refresh mr-2"></i>Повторить тест
                </button>
            </div>
            
            <!-- PHP Info -->
            <div class="bg-gray-50 rounded-lg p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">📋 Информация о PHP</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <strong>Настройки загрузки файлов:</strong>
                        <ul class="mt-2 space-y-1 text-gray-600">
                            <li>file_uploads: <?php echo ini_get('file_uploads') ? 'Включено' : 'Отключено'; ?></li>
                            <li>upload_max_filesize: <?php echo ini_get('upload_max_filesize'); ?></li>
                            <li>post_max_size: <?php echo ini_get('post_max_size'); ?></li>
                            <li>max_file_uploads: <?php echo ini_get('max_file_uploads'); ?></li>
                        </ul>
                    </div>
                    <div>
                        <strong>Пути:</strong>
                        <ul class="mt-2 space-y-1 text-gray-600">
                            <li>Document Root: <?php echo $_SERVER['DOCUMENT_ROOT'] ?? 'N/A'; ?></li>
                            <li>Script Path: <?php echo __DIR__; ?></li>
                            <li>Upload Dir: <?php echo __DIR__ . '/uploads/transactions/'; ?></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
