/* Text Contrast Improvements for Poseidon Rental Pro */

/* Main text contrast fixes ONLY for light backgrounds (not footer) */
body:not(footer) .text-gray-600,
.bg-white .text-gray-600,
.bg-gray-50 .text-gray-600,
.bg-gray-100 .text-gray-600 {
    color: #374151 !important; /* gray-700 - much better contrast */
}

body:not(footer) .text-gray-500,
.bg-white .text-gray-500,
.bg-gray-50 .text-gray-500,
.bg-gray-100 .text-gray-500 {
    color: #4b5563 !important; /* gray-600 - better contrast */
}

body:not(footer) .text-gray-400,
.bg-white .text-gray-400,
.bg-gray-50 .text-gray-400,
.bg-gray-100 .text-gray-400 {
    color: #6b7280 !important; /* gray-500 - better contrast */
}

/* Footer keeps original colors for dark background */
footer .text-gray-100 {
    color: #f3f4f6 !important; /* Keep light for dark background */
}

footer .text-gray-200 {
    color: #e5e7eb !important; /* Keep light for dark background */
}

footer .text-gray-300 {
    color: #d1d5db !important; /* Keep light for dark background */
}

footer .text-gray-400 {
    color: #9ca3af !important; /* Keep original for dark background */
}

footer .text-gray-500 {
    color: #6b7280 !important; /* Keep original for dark background */
}

/* Override light text colors on gradient backgrounds */
.gradient-bg .text-blue-100,
.gradient-bg .text-blue-200,
.gradient-bg .text-blue-300 {
    color: #e5e7eb !important; /* gray-200 - much more visible */
}

.gradient-bg .text-gray-100,
.gradient-bg .text-gray-200 {
    color: #e5e7eb !important; /* gray-200 */
}

.gradient-bg .text-gray-300 {
    color: #d1d5db !important; /* gray-300 */
}

footer .text-blue-100,
footer .text-blue-200,
footer .text-blue-300 {
    color: #e5e7eb !important; /* gray-200 */
}

footer .text-gray-200 {
    color: #e5e7eb !important; /* gray-200 */
}

footer .text-gray-300 {
    color: #d1d5db !important; /* gray-300 */
}

/* Newsletter and CTA sections */
.bg-gradient-to-r .text-blue-200,
.bg-gradient-to-r .text-gray-200 {
    color: #e5e7eb !important; /* gray-200 */
}

.bg-gradient-to-r .text-blue-300,
.bg-gradient-to-r .text-gray-300 {
    color: #d1d5db !important; /* gray-300 */
}

/* Hero sections */
.hero-section .text-blue-100,
.hero-section .text-blue-200 {
    color: #e5e7eb !important; /* gray-200 */
}

/* Stats sections */
.stats-section .text-blue-200 {
    color: #d1d5db !important; /* gray-300 */
}

/* Card text on dark backgrounds */
.bg-primary .text-blue-200,
.bg-primary .text-gray-200 {
    color: #e5e7eb !important; /* gray-200 */
}

.bg-primary .text-blue-300,
.bg-primary .text-gray-300 {
    color: #d1d5db !important; /* gray-300 */
}

/* Ensure minimum contrast ratio */
.text-contrast-high {
    color: #f3f4f6 !important; /* gray-100 - highest contrast */
}

.text-contrast-medium {
    color: #e5e7eb !important; /* gray-200 - medium contrast */
}

.text-contrast-low {
    color: #d1d5db !important; /* gray-300 - lower contrast but still readable */
}

/* Dark background text improvements */
.bg-dark .text-light,
.bg-gradient .text-light {
    color: #f9fafb !important; /* gray-50 - very light */
}

/* Mobile responsive text contrast */
@media (max-width: 768px) {
    .gradient-bg .text-blue-200,
    .gradient-bg .text-gray-200 {
        color: #f3f4f6 !important; /* Even lighter on mobile for better readability */
    }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .gradient-bg .text-blue-200,
    .gradient-bg .text-gray-200,
    footer .text-blue-200,
    footer .text-gray-200 {
        color: #ffffff !important; /* Pure white for high contrast */
    }
}

/* Print styles - ensure dark text on white background */
@media print {
    .gradient-bg .text-blue-200,
    .gradient-bg .text-gray-200,
    footer .text-blue-200,
    footer .text-gray-200 {
        color: #1f2937 !important; /* Dark gray for print */
    }
}
