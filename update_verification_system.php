<?php
require_once 'config/database.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Обновление системы верификации</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen'>
<div class='container mx-auto px-4 py-8'>";

echo "<div class='max-w-4xl mx-auto'>
        <div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h1 class='text-2xl font-bold text-gray-900 mb-4'>
                <i class='fas fa-shield-alt text-blue-500 mr-2'></i>
                Обновление системы верификации
            </h1>
            <p class='text-gray-600'>Добавление полей верификации в базу данных...</p>
        </div>";

try {
    $db = getDB();
    
    // Check if verification_status column exists
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка структуры таблицы users</h2>";
    
    $stmt = $db->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (!in_array('verification_status', $columns)) {
        echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-2'></i>Добавление поля verification_status...</p>";
        $db->exec("ALTER TABLE users ADD COLUMN verification_status ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified'");
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Поле verification_status добавлено</p>";
    } else {
        echo "<p class='text-blue-600'><i class='fas fa-info-circle mr-2'></i>Поле verification_status уже существует</p>";
    }
    
    echo "</div>";
    
    // Check if user_verifications table exists
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка таблицы user_verifications</h2>";
    
    $stmt = $db->prepare("SHOW TABLES LIKE 'user_verifications'");
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-2'></i>Создание таблицы user_verifications...</p>";
        $db->exec("
            CREATE TABLE user_verifications (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                first_name VARCHAR(100) NOT NULL,
                last_name VARCHAR(100) NOT NULL,
                birth_date DATE NOT NULL,
                passport_photo_path VARCHAR(500) NOT NULL,
                status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
                admin_notes TEXT NULL,
                submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                processed_at TIMESTAMP NULL,
                processed_by INT NULL,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                FOREIGN KEY (processed_by) REFERENCES users(id) ON DELETE SET NULL
            )
        ");
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Таблица user_verifications создана</p>";
    } else {
        echo "<p class='text-blue-600'><i class='fas fa-info-circle mr-2'></i>Таблица user_verifications уже существует</p>";
    }
    
    echo "</div>";
    
    // Create upload directory
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Создание директорий для загрузок</h2>";
    
    $upload_dir = 'uploads/verifications';
    if (!is_dir($upload_dir)) {
        if (mkdir($upload_dir, 0755, true)) {
            echo "<p class='text-green-600'><i class='fas fa-folder-plus mr-2'></i>Создана директория: $upload_dir</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка создания директории: $upload_dir</p>";
        }
    } else {
        echo "<p class='text-blue-600'><i class='fas fa-folder mr-2'></i>Директория уже существует: $upload_dir</p>";
    }
    
    echo "</div>";
    
    // Test verification functions
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тестирование функций верификации</h2>";
    
    require_once 'includes/functions.php';
    
    // Test getUserVerificationStatus function
    try {
        $status = getUserVerificationStatus(1); // Test with user ID 1
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция getUserVerificationStatus работает</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ошибка функции getUserVerificationStatus: " . $e->getMessage() . "</p>";
    }
    
    // Test isUserVerified function
    try {
        $verified = isUserVerified(1); // Test with user ID 1
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция isUserVerified работает</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ошибка функции isUserVerified: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Обновление завершено!</h2>
            <p class='text-green-700 mb-4'>Система верификации успешно установлена и готова к использованию.</p>
            <div class='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <a href='pages/verification.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-shield-alt mr-2'></i>Страница верификации
                </a>
                <a href='admin/verifications.php' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель верификаций
                </a>
                <a href='pages/dashboard.php' class='bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Дашборд
                </a>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка</h2>
            <p class='text-red-700'>Ошибка обновления базы данных: " . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></body></html>";
?>
