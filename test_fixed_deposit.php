<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Simulate logged in user for testing
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['username'] = 'testuser';
    $_SESSION['email'] = '<EMAIL>';
}

$user_id = $_SESSION['user_id'];

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест исправленного deposit.php</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🧪 Тест исправленного deposit.php</h1>";

$all_tests_passed = true;

// Test 1: Check if deposit.php was fixed
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📄 Тест 1: Проверка исправлений в deposit.php</h2>";

$deposit_file = 'pages/deposit.php';
if (file_exists($deposit_file)) {
    $deposit_content = file_get_contents($deposit_file);
    
    $fixes_check = [
        'Исправлена функция копирования адреса' => strpos($deposit_content, 'fallbackCopyTextToClipboard') !== false,
        'Добавлена расширенная отладка' => strpos($deposit_content, 'FIXED DEPOSIT PAGE DEBUG') !== false,
        'Улучшена обработка файлов' => strpos($deposit_content, 'handleFileSelect') !== false,
        'Правильная структура формы' => strpos($deposit_content, 'enctype="multipart/form-data"') !== false,
        'CSRF защита присутствует' => strpos($deposit_content, 'csrf_token') !== false,
        'Использует processDepositRequest' => strpos($deposit_content, 'processDepositRequest') !== false,
        'Добавлена валидация формы' => strpos($deposit_content, 'validateDepositForm') !== false,
        'Добавлена проверка файла перед обработкой' => strpos($deposit_content, 'File validation failed') !== false
    ];
    
    foreach ($fixes_check as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        
        if (!$result) {
            $all_tests_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл deposit.php не найден</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 2: Test file upload functionality
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📤 Тест 2: Функциональность загрузки файлов</h2>";

// Create a test image
$test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$test_file_path = 'test_fixed_deposit_upload.png';
file_put_contents($test_file_path, $test_image_data);

$test_file = [
    'name' => 'test_fixed_deposit_upload.png',
    'type' => 'image/png',
    'size' => strlen($test_image_data),
    'tmp_name' => $test_file_path,
    'error' => 0
];

// Test the complete flow
if (function_exists('processDepositRequest')) {
    $result = processDepositRequest($user_id, 40.00, $test_file);
    
    if ($result['success']) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>processDepositRequest работает корректно</p>";
        
        // Check if file was saved
        try {
            $db = getDB();
            $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? ORDER BY created_at DESC LIMIT 1");
            $stmt->execute([$user_id]);
            $latest_transaction = $stmt->fetch();
            
            if ($latest_transaction && !empty($latest_transaction['screenshot_path'])) {
                $screenshot_path = $latest_transaction['screenshot_path'];
                echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл сохранен в БД: $screenshot_path</p>";
                
                if (file_exists($screenshot_path)) {
                    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл существует физически</p>";
                    
                    // Test admin access
                    $admin_path = 'admin/../' . $screenshot_path;
                    if (file_exists($admin_path)) {
                        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл доступен из админ-панели</p>";
                    } else {
                        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл НЕ доступен из админ-панели</p>";
                        echo "<p class='text-gray-600 text-sm ml-4'>Проверяемый путь: $admin_path</p>";
                        $all_tests_passed = false;
                    }
                    
                    // Test direct URL access
                    $file_url = 'http://localhost/poseidon/' . $screenshot_path;
                    echo "<p class='text-blue-600 mb-2'><i class='fas fa-link mr-2'></i>URL файла: <a href='$file_url' target='_blank' class='underline'>$file_url</a></p>";
                    
                    // Clean up
                    unlink($screenshot_path);
                } else {
                    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл НЕ существует физически</p>";
                    $all_tests_passed = false;
                }
            } else {
                echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>В БД нет пути к скриншоту</p>";
                $all_tests_passed = false;
            }
        } catch (Exception $e) {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка БД: " . $e->getMessage() . "</p>";
            $all_tests_passed = false;
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка processDepositRequest: " . $result['message'] . "</p>";
        $all_tests_passed = false;
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция processDepositRequest не найдена</p>";
    $all_tests_passed = false;
}

// Clean up test file
if (file_exists($test_file_path)) {
    unlink($test_file_path);
}

echo "</div>";

// Test 3: Check admin panel compatibility
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>⚙️ Тест 3: Совместимость с админ-панелью</h2>";

try {
    $db = getDB();
    
    // Get recent transactions with screenshots
    $stmt = $db->prepare("SELECT * FROM transactions WHERE user_id = ? AND screenshot_path IS NOT NULL ORDER BY created_at DESC LIMIT 3");
    $stmt->execute([$user_id]);
    $transactions = $stmt->fetchAll();
    
    if (!empty($transactions)) {
        echo "<h3 class='font-bold text-blue-900 mb-3'>📋 Последние транзакции с файлами:</h3>";
        
        foreach ($transactions as $transaction) {
            $screenshot_path = $transaction['screenshot_path'];
            $amount = $transaction['amount'];
            $status = $transaction['status'];
            $created_at = $transaction['created_at'];
            
            echo "<div class='border border-gray-200 rounded-lg p-4 mb-3'>";
            echo "<p class='font-semibold'>ID: {$transaction['id']} | Сумма: \${$amount} | Статус: {$status}</p>";
            echo "<p class='text-sm text-gray-600'>Создано: {$created_at}</p>";
            echo "<p class='text-sm'><strong>Путь к файлу:</strong> {$screenshot_path}</p>";
            
            // Check file accessibility
            if (file_exists($screenshot_path)) {
                echo "<p class='text-green-600 text-sm'><i class='fas fa-check mr-1'></i>Файл существует</p>";
                
                // Check admin access
                $admin_path = 'admin/../' . $screenshot_path;
                if (file_exists($admin_path)) {
                    echo "<p class='text-green-600 text-sm'><i class='fas fa-check mr-1'></i>Доступен из админ-панели</p>";
                } else {
                    echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>НЕ доступен из админ-панели</p>";
                    $all_tests_passed = false;
                }
            } else {
                echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Файл НЕ существует</p>";
                $all_tests_passed = false;
            }
            
            echo "</div>";
        }
    } else {
        echo "<p class='text-gray-600'><i class='fas fa-info mr-2'></i>Нет транзакций с файлами для проверки</p>";
    }
    
} catch (Exception $e) {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ошибка БД: " . $e->getMessage() . "</p>";
    $all_tests_passed = false;
}

echo "</div>";

// Test 4: JavaScript functionality test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🖱️ Тест 4: JavaScript функциональность</h2>";

echo "<div class='bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4'>
        <h4 class='font-bold text-blue-900 mb-2'>Тест кнопки копирования адреса:</h4>
        <div class='flex items-center space-x-3'>
            <code id='testWalletAddress' class='bg-white p-2 rounded border flex-1'>TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE</code>
            <button onclick='testCopyFunction()' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded transition-colors'>
                <i class='fas fa-copy mr-2'></i>Тест копирования
            </button>
        </div>
        <p class='text-sm text-blue-700 mt-2'>Нажмите кнопку выше, чтобы протестировать функцию копирования</p>
      </div>";

echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4'>
        <h4 class='font-bold text-green-900 mb-2'>Тест валидации файлов:</h4>
        <input type='file' id='testFileInput' accept='image/*' onchange='testFileValidation(this)' class='mb-2'>
        <div id='testFileResult' class='text-sm'></div>
      </div>";

echo "</div>";

// Final Results
if ($all_tests_passed) {
    echo "<div class='bg-gradient-to-r from-green-500 to-emerald-600 rounded-2xl p-8 text-white text-center'>
            <div class='text-6xl mb-4'>
                <i class='fas fa-trophy'></i>
            </div>
            <h2 class='text-3xl font-bold mb-4'>🎉 ВСЕ ТЕСТЫ ПРОЙДЕНЫ!</h2>
            <p class='text-xl mb-6 opacity-90'>deposit.php полностью исправлен и работает корректно</p>
            
            <div class='bg-white bg-opacity-20 rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold mb-3'>✅ Исправленные проблемы:</h3>
                <ul class='text-left space-y-2 opacity-90'>
                    <li>• Загрузка файлов работает корректно</li>
                    <li>• Файлы сохраняются в унифицированную директорию</li>
                    <li>• Файлы отображаются в админ-панели</li>
                    <li>• Кнопка копирования адреса работает</li>
                    <li>• Добавлена расширенная отладка</li>
                    <li>• Улучшена валидация файлов</li>
                </ul>
            </div>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/deposit.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-plus mr-2'></i>Тест Deposit
                </a>
                <a href='admin/transactions.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='pages/dashboard.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Dashboard
                </a>
                <a href='pages/withdrawal.php' class='bg-white text-green-600 px-6 py-3 rounded-lg font-bold hover:bg-green-50 transition-colors'>
                    <i class='fas fa-minus mr-2'></i>Withdrawal
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>⚠️ Некоторые тесты не прошли</h2>
            <p class='text-red-700 text-lg mb-6'>Требуется дополнительная диагностика</p>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='diagnose_deposit_issue.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-search mr-2'></i>Диагностика
                </a>
                <a href='fix_deposit_upload.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-wrench mr-2'></i>Повторить исправления
                </a>
                <a href='pages/deposit.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-plus mr-2'></i>Проверить Deposit
                </a>
            </div>
          </div>";
}

echo "<script>
function testCopyFunction() {
    const walletAddress = document.getElementById('testWalletAddress').textContent.trim();
    
    if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(walletAddress).then(function() {
            alert('✅ Адрес скопирован успешно: ' + walletAddress);
        }).catch(function() {
            fallbackCopyTest(walletAddress);
        });
    } else {
        fallbackCopyTest(walletAddress);
    }
}

function fallbackCopyTest(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
        document.execCommand('copy');
        alert('✅ Адрес скопирован (fallback): ' + text);
    } catch (err) {
        alert('❌ Ошибка копирования: ' + err);
    }
    
    document.body.removeChild(textArea);
}

function testFileValidation(input) {
    const result = document.getElementById('testFileResult');
    const file = input.files[0];
    
    if (file) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        const maxSize = 5 * 1024 * 1024; // 5MB
        
        let messages = [];
        
        if (allowedTypes.includes(file.type)) {
            messages.push('<span class=\"text-green-600\">✅ Тип файла валиден: ' + file.type + '</span>');
        } else {
            messages.push('<span class=\"text-red-600\">❌ Недопустимый тип файла: ' + file.type + '</span>');
        }
        
        if (file.size <= maxSize) {
            messages.push('<span class=\"text-green-600\">✅ Размер файла валиден: ' + formatFileSize(file.size) + '</span>');
        } else {
            messages.push('<span class=\"text-red-600\">❌ Файл слишком большой: ' + formatFileSize(file.size) + '</span>');
        }
        
        result.innerHTML = messages.join('<br>');
    } else {
        result.innerHTML = '<span class=\"text-gray-600\">Файл не выбран</span>';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script>";

echo "</div></body></html>";
?>
