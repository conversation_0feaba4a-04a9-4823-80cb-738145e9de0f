<?php
// Quick test to verify blog image fixes
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html>
<head>
    <title>Quick Blog Image Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background: #d4edda; color: #155724; }
        .fail { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>";

echo "<h1>🧪 Quick Blog Image Test</h1>";

// Test 1: Functions exist
echo "<div class='test " . (function_exists('getBlogPostImage') ? 'pass' : 'fail') . "'>";
echo "✓ Function getBlogPostImage: " . (function_exists('getBlogPostImage') ? 'EXISTS' : 'MISSING');
echo "</div>";

echo "<div class='test " . (function_exists('getEscapedBlogImage') ? 'pass' : 'fail') . "'>";
echo "✓ Function getEscapedBlogImage: " . (function_exists('getEscapedBlogImage') ? 'EXISTS' : 'MISSING');
echo "</div>";

// Test 2: Function behavior
if (function_exists('getBlogPostImage')) {
    // Test with image
    $post_with_image = ['image_url' => 'test.jpg'];
    $result1 = getBlogPostImage($post_with_image);
    echo "<div class='test " . ($result1 === 'test.jpg' ? 'pass' : 'fail') . "'>";
    echo "✓ Post with image: " . ($result1 === 'test.jpg' ? 'CORRECT' : 'INCORRECT') . " (got: $result1)";
    echo "</div>";
    
    // Test without image
    $post_without_image = ['title' => 'Test'];
    $result2 = getBlogPostImage($post_without_image);
    $expected = 'https://lim-english.com/uploads/images/all/img_articles_new/news_in_english.jpg';
    echo "<div class='test " . ($result2 === $expected ? 'pass' : 'fail') . "'>";
    echo "✓ Post without image: " . ($result2 === $expected ? 'CORRECT FALLBACK' : 'INCORRECT FALLBACK');
    echo "</div>";
    
    // Test escaping
    if (function_exists('getEscapedBlogImage')) {
        $post_special = ['image_url' => 'test&special.jpg'];
        $result3 = getEscapedBlogImage($post_special);
        $expected3 = htmlspecialchars('test&special.jpg');
        echo "<div class='test " . ($result3 === $expected3 ? 'pass' : 'fail') . "'>";
        echo "✓ HTML escaping: " . ($result3 === $expected3 ? 'WORKING' : 'NOT WORKING');
        echo "</div>";
    }
}

// Test 3: File modifications
$files_check = [
    'pages/blog.php' => 'getEscapedBlogImage',
    'pages/blog-post.php' => 'getEscapedBlogImage'
];

foreach ($files_check as $file => $pattern) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $has_pattern = strpos($content, $pattern) !== false;
        $has_old_pattern = strpos($content, "['image_url']") !== false;
        
        echo "<div class='test " . ($has_pattern && !$has_old_pattern ? 'pass' : ($has_pattern ? 'info' : 'fail')) . "'>";
        echo "✓ $file: ";
        if ($has_pattern && !$has_old_pattern) {
            echo "FULLY UPDATED";
        } elseif ($has_pattern) {
            echo "PARTIALLY UPDATED (still has old patterns)";
        } else {
            echo "NOT UPDATED";
        }
        echo "</div>";
    } else {
        echo "<div class='test fail'>✗ $file: FILE NOT FOUND</div>";
    }
}

echo "<h2>🎯 Summary</h2>";
echo "<div class='test info'>";
echo "Default fallback image: <br>";
echo "<code>https://lim-english.com/uploads/images/all/img_articles_new/news_in_english.jpg</code>";
echo "</div>";

echo "<div class='test info'>";
echo "Test your blog pages:<br>";
echo "<a href='pages/blog.php' target='_blank'>Blog Page</a> | ";
echo "<a href='test_blog_image_fixes.php' target='_blank'>Detailed Tests</a>";
echo "</div>";

echo "</body></html>";
?>
