<?php
/**
 * Daily Profit Calculation Cron Job
 * 
 * This script should be run daily via cron to calculate and distribute
 * daily profits to all active investments.
 * 
 * Cron command example:
 * 0 0 * * * /usr/bin/php /path/to/your/project/cron/daily_profit.php
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../includes/functions.php';

// Ensure this script is only run from command line or by authorized users
if (php_sapi_name() !== 'cli' && !isAdmin()) {
    die('Unauthorized access');
}

$log_file = __DIR__ . '/../logs/daily_profit_' . date('Y-m-d') . '.log';

function logMessage($message) {
    global $log_file;
    $timestamp = date('Y-m-d H:i:s');
    file_put_contents($log_file, "[$timestamp] $message" . PHP_EOL, FILE_APPEND | LOCK_EX);
    echo "[$timestamp] $message" . PHP_EOL;
}

try {
    logMessage("Starting daily profit calculation...");
    
    $db = getDB();
    
    // Get all active investments
    $stmt = $db->prepare("
        SELECT ui.*, u.id as user_id, u.balance 
        FROM user_investments ui 
        JOIN users u ON ui.user_id = u.id 
        WHERE ui.is_active = 1 
        AND ui.end_date >= CURDATE()
        AND ui.start_date <= CURDATE()
    ");
    $stmt->execute();
    $active_investments = $stmt->fetchAll();
    
    logMessage("Found " . count($active_investments) . " active investments");
    
    $total_profit_distributed = 0;
    $successful_distributions = 0;
    $failed_distributions = 0;
    
    foreach ($active_investments as $investment) {
        try {
            // Check if profit has already been calculated for today
            $today = date('Y-m-d');
            $stmt = $db->prepare("
                SELECT COUNT(*) as count 
                FROM daily_profits 
                WHERE user_investment_id = ? AND profit_date = ?
            ");
            $stmt->execute([$investment['id'], $today]);
            $existing = $stmt->fetch();
            
            if ($existing['count'] > 0) {
                logMessage("Profit already calculated for investment ID {$investment['id']} today");
                continue;
            }
            
            // Calculate daily profit
            $daily_profit = calculateDailyProfit(
                $investment['id'], 
                $investment['amount'], 
                $investment['monthly_rate']
            );
            
            if ($daily_profit <= 0) {
                logMessage("Invalid profit calculation for investment ID {$investment['id']}");
                $failed_distributions++;
                continue;
            }
            
            $db->beginTransaction();
            
            // Add daily profit record
            $stmt = $db->prepare("
                INSERT INTO daily_profits (user_investment_id, profit_amount, profit_date) 
                VALUES (?, ?, ?)
            ");
            $stmt->execute([$investment['id'], $daily_profit, $today]);
            
            // Update user balance
            $stmt = $db->prepare("
                UPDATE users 
                SET balance = balance + ? 
                WHERE id = ?
            ");
            $stmt->execute([$daily_profit, $investment['user_id']]);
            
            // Update total profit in user_investments
            $stmt = $db->prepare("
                UPDATE user_investments 
                SET total_profit = total_profit + ? 
                WHERE id = ?
            ");
            $stmt->execute([$daily_profit, $investment['id']]);
            
            $db->commit();
            
            $total_profit_distributed += $daily_profit;
            $successful_distributions++;
            
            logMessage("Distributed $" . number_format($daily_profit, 2) . " to user ID {$investment['user_id']} for investment ID {$investment['id']}");
            
        } catch (Exception $e) {
            $db->rollback();
            $failed_distributions++;
            logMessage("Error processing investment ID {$investment['id']}: " . $e->getMessage());
        }
    }
    
    // Check for expired investments and mark them as inactive
    $stmt = $db->prepare("
        UPDATE user_investments 
        SET is_active = 0 
        WHERE end_date < CURDATE() AND is_active = 1
    ");
    $stmt->execute();
    $expired_investments = $stmt->rowCount();
    
    if ($expired_investments > 0) {
        logMessage("Marked $expired_investments investments as expired");
        
        // If capital return is enabled, return the capital to users
        $stmt = $db->prepare("
            SELECT ui.*, i.capital_return 
            FROM user_investments ui 
            JOIN investments i ON ui.investment_id = i.id 
            WHERE ui.end_date < CURDATE() 
            AND ui.is_active = 0 
            AND i.capital_return = 1
            AND ui.id NOT IN (
                SELECT DISTINCT user_investment_id 
                FROM daily_profits 
                WHERE profit_date = CURDATE() 
                AND profit_amount = (
                    SELECT amount 
                    FROM user_investments ui2 
                    WHERE ui2.id = daily_profits.user_investment_id
                )
            )
        ");
        $stmt->execute();
        $capital_returns = $stmt->fetchAll();
        
        foreach ($capital_returns as $return_investment) {
            try {
                $db->beginTransaction();
                
                // Return capital to user
                $stmt = $db->prepare("
                    UPDATE users 
                    SET balance = balance + ? 
                    WHERE id = ?
                ");
                $stmt->execute([$return_investment['amount'], $return_investment['user_id']]);
                
                // Log the capital return as a special daily profit entry
                $stmt = $db->prepare("
                    INSERT INTO daily_profits (user_investment_id, profit_amount, profit_date) 
                    VALUES (?, ?, ?)
                ");
                $stmt->execute([$return_investment['id'], $return_investment['amount'], $today]);
                
                $db->commit();
                
                logMessage("Returned capital $" . number_format($return_investment['amount'], 2) . " to user ID {$return_investment['user_id']}");
                
            } catch (Exception $e) {
                $db->rollback();
                logMessage("Error returning capital for investment ID {$return_investment['id']}: " . $e->getMessage());
            }
        }
    }
    
    // Summary
    logMessage("=== DAILY PROFIT CALCULATION SUMMARY ===");
    logMessage("Total active investments processed: " . count($active_investments));
    logMessage("Successful distributions: $successful_distributions");
    logMessage("Failed distributions: $failed_distributions");
    logMessage("Total profit distributed: $" . number_format($total_profit_distributed, 2));
    logMessage("Expired investments: $expired_investments");
    logMessage("Daily profit calculation completed successfully");
    
} catch (Exception $e) {
    logMessage("FATAL ERROR: " . $e->getMessage());
    logMessage("Daily profit calculation failed");
    
    // You might want to send an email notification here
    // mail(ADMIN_EMAIL, 'Daily Profit Calculation Failed', $e->getMessage());
}

// Clean up old log files (keep only last 30 days)
$log_dir = __DIR__ . '/../logs/';
if (is_dir($log_dir)) {
    $files = glob($log_dir . 'daily_profit_*.log');
    foreach ($files as $file) {
        if (filemtime($file) < strtotime('-30 days')) {
            unlink($file);
        }
    }
}
?>
