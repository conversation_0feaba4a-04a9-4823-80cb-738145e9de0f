<?php
require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тестирование критических исправлений</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen'>
<div class='container mx-auto px-4 py-8'>";

echo "<div class='max-w-4xl mx-auto'>
        <div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h1 class='text-2xl font-bold text-gray-900 mb-4'>
                <i class='fas fa-bug-slash text-red-500 mr-2'></i>
                Тестирование критических исправлений
            </h1>
            <p class='text-gray-600'>Проверка исправлений flash сообщений в регистрации и undefined array key ошибок</p>
        </div>";

try {
    // Test 1: Flash message functions
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 1: Flash сообщения</h2>";
    
    $flash_functions = ['setFlashMessage', 'getFlashMessage', 'displayFlashMessages'];
    foreach ($flash_functions as $func) {
        if (function_exists($func)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция $func существует</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция $func не найдена</p>";
        }
    }
    
    // Test flash message functionality
    if (function_exists('setFlashMessage') && function_exists('getFlashMessage')) {
        setFlashMessage('test', 'Тестовое сообщение');
        $test_message = getFlashMessage('test');
        
        if ($test_message === 'Тестовое сообщение') {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Flash сообщения работают корректно</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Flash сообщения не работают</p>";
        }
    }
    
    echo "</div>";
    
    // Test 2: Image functions
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 2: Функции изображений</h2>";
    
    $image_functions = ['getBlogPostImage', 'getEscapedBlogImage', 'getInvestmentImage', 'getEscapedInvestmentImage'];
    foreach ($image_functions as $func) {
        if (function_exists($func)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция $func существует</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция $func не найдена</p>";
        }
    }
    
    // Test image functions with edge cases
    if (function_exists('getBlogPostImage') && function_exists('getInvestmentImage')) {
        // Test with empty array
        $empty_post = [];
        $blog_result = getBlogPostImage($empty_post);
        $investment_result = getInvestmentImage($empty_post);
        
        if (!empty($blog_result) && !empty($investment_result)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функции изображений обрабатывают пустые массивы</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функции изображений не обрабатывают пустые массивы</p>";
        }
    }
    
    echo "</div>";
    
    // Test 3: File modifications check
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 3: Проверка исправлений в файлах</h2>";
    
    $files_to_check = [
        'pages/register.php' => [
            'patterns' => ['getFlashMessage(\'success\')', 'getFlashMessage(\'error\')', 'getFlashMessage(\'info\')'],
            'description' => 'Flash сообщения в регистрации'
        ],
        'index.php' => [
            'patterns' => ['getEscapedBlogImage($post)', 'getEscapedInvestmentImage($investment)'],
            'description' => 'Безопасные изображения на главной'
        ],
        'pages/investments.php' => [
            'patterns' => ['getEscapedInvestmentImage($investment)'],
            'description' => 'Безопасные изображения инвестиций'
        ],
        'pages/investment-detail.php' => [
            'patterns' => ['getEscapedInvestmentImage($investment)', 'getEscapedInvestmentImage($similar)'],
            'description' => 'Безопасные изображения в деталях'
        ]
    ];
    
    foreach ($files_to_check as $file => $check) {
        if (file_exists($file)) {
            $content = file_get_contents($file);
            $all_patterns_found = true;
            
            echo "<h3 class='font-semibold text-gray-800 mb-2'>$file - {$check['description']}:</h3>";
            
            foreach ($check['patterns'] as $pattern) {
                if (strpos($content, $pattern) !== false) {
                    echo "<p class='text-green-600 ml-4'><i class='fas fa-check mr-2'></i>Найден: $pattern</p>";
                } else {
                    echo "<p class='text-red-600 ml-4'><i class='fas fa-times mr-2'></i>Не найден: $pattern</p>";
                    $all_patterns_found = false;
                }
            }
            
            // Check for old problematic patterns
            $old_patterns = ["\$post['image_url']", "\$investment['image_url']", "\$similar['image_url']"];
            $has_old_patterns = false;
            
            foreach ($old_patterns as $old_pattern) {
                if (strpos($content, $old_pattern) !== false) {
                    $has_old_patterns = true;
                    break;
                }
            }
            
            if (!$has_old_patterns) {
                echo "<p class='text-green-600 ml-4'><i class='fas fa-shield-alt mr-2'></i>Старые небезопасные паттерны удалены</p>";
            } else {
                echo "<p class='text-yellow-600 ml-4'><i class='fas fa-exclamation-triangle mr-2'></i>Найдены старые небезопасные паттерны</p>";
            }
            
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл $file не найден</p>";
        }
        echo "<br>";
    }
    
    echo "</div>";
    
    // Test 4: Registration page flash messages
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 4: Flash сообщения в регистрации</h2>";
    
    if (file_exists('pages/register.php')) {
        $register_content = file_get_contents('pages/register.php');
        
        $flash_checks = [
            'getFlashMessage(\'success\')' => 'Получение success сообщений',
            'getFlashMessage(\'error\')' => 'Получение error сообщений',
            'getFlashMessage(\'info\')' => 'Получение info сообщений',
            'htmlspecialchars($flash_success)' => 'Экранирование success сообщений',
            'htmlspecialchars($flash_error)' => 'Экранирование error сообщений'
        ];
        
        foreach ($flash_checks as $pattern => $description) {
            if (strpos($register_content, $pattern) !== false) {
                echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>$description: ✓</p>";
            } else {
                echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>$description: ✗</p>";
            }
        }
    }
    
    echo "</div>";
    
    // Test 5: Visual demonstration
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 5: Визуальная демонстрация</h2>";
    
    // Test flash messages
    setFlashMessage('success', 'Тест успешного сообщения');
    setFlashMessage('error', 'Тест сообщения об ошибке');
    setFlashMessage('info', 'Тест информационного сообщения');
    
    echo "<h3 class='font-semibold mb-3'>Демонстрация flash сообщений:</h3>";
    displayFlashMessages();
    
    // Test image functions
    echo "<h3 class='font-semibold mb-3 mt-6'>Демонстрация функций изображений:</h3>";
    
    $test_items = [
        ['title' => 'Элемент с изображением', 'image_url' => 'https://via.placeholder.com/200x150/4F46E5/FFFFFF?text=Custom'],
        ['title' => 'Элемент без изображения'],
        ['title' => 'Элемент с пустым изображением', 'image_url' => '']
    ];
    
    echo "<div class='grid grid-cols-1 md:grid-cols-3 gap-4'>";
    foreach ($test_items as $item) {
        $blog_image = getEscapedBlogImage($item);
        $investment_image = getEscapedInvestmentImage($item);
        
        echo "<div class='border rounded-lg p-4'>
                <h4 class='font-medium mb-2'>" . htmlspecialchars($item['title']) . "</h4>
                <div class='space-y-2'>
                    <div>
                        <p class='text-xs text-gray-600 mb-1'>Blog image:</p>
                        <img src='$blog_image' alt='Blog' class='w-full h-20 object-cover rounded'>
                    </div>
                    <div>
                        <p class='text-xs text-gray-600 mb-1'>Investment image:</p>
                        <img src='$investment_image' alt='Investment' class='w-full h-20 object-cover rounded'>
                    </div>
                </div>
              </div>";
    }
    echo "</div>";
    
    echo "</div>";
    
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Тестирование завершено!</h2>
            <p class='text-green-700 mb-4'>Критические исправления протестированы и готовы к использованию.</p>
            
            <div class='grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'>
                <a href='pages/register.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-user-plus mr-2'></i>Тест регистрации
                </a>
                <a href='index.php' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-home mr-2'></i>Главная страница
                </a>
                <a href='pages/investments.php' class='bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-chart-line mr-2'></i>Инвестиции
                </a>
            </div>
            
            <div class='bg-white rounded-lg p-4'>
                <h3 class='font-bold text-gray-900 mb-2'>Исправленные проблемы:</h3>
                <ul class='text-sm text-gray-700 space-y-1'>
                    <li>✅ Добавлены flash сообщения в pages/register.php</li>
                    <li>✅ Исправлены undefined array key ошибки в index.php</li>
                    <li>✅ Исправлены изображения в pages/investments.php</li>
                    <li>✅ Исправлены изображения в pages/investment-detail.php</li>
                    <li>✅ Добавлены функции getInvestmentImage() и getEscapedInvestmentImage()</li>
                    <li>✅ Обеспечена безопасность и консистентность изображений</li>
                </ul>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка</h2>
            <p class='text-red-700'>Ошибка тестирования: " . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></body></html>";
?>
