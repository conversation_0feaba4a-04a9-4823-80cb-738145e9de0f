<?php
require_once 'config/database.php';
require_once 'includes/functions.php';
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест мобильной навигации</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for better mobile navigation */
        .mobile-nav-test {
            background: linear-gradient(135deg, #1e293b 0%, #1e40af 100%);
        }
        
        .mobile-menu-item {
            transition: all 0.3s ease;
        }
        
        .mobile-menu-item:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateX(5px);
        }
        
        /* Debug styles */
        .debug-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Debug Info -->
    <div class="debug-info">
        <div>Screen Width: <span id="screen-width"></span>px</div>
        <div>Menu Status: <span id="menu-status">Closed</span></div>
    </div>

    <!-- Navigation -->
    <nav class="fixed top-0 left-0 right-0 z-50 mobile-nav-test backdrop-blur-lg border-b border-white border-opacity-10">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <a href="index.php" class="flex items-center space-x-3 group">
                        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center transform group-hover:scale-110 transition-all duration-300 shadow-lg">
                            <i class="fas fa-anchor text-white text-lg"></i>
                        </div>
                        <div class="flex flex-col">
                            <span class="text-lg font-bold text-white">Poseidon</span>
                            <span class="text-xs text-blue-200 -mt-1">Test Navigation</span>
                        </div>
                    </a>
                </div>

                <!-- Desktop Menu (hidden on mobile) -->
                <div class="hidden md:flex items-center space-x-1">
                    <a href="#" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium">Home</a>
                    <a href="#" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium">Investments</a>
                    <a href="#" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium">Blog</a>
                    <a href="#" class="px-4 py-2 rounded-lg text-gray-300 hover:text-white hover:bg-white hover:bg-opacity-10 transition-all duration-300 font-medium">Contact</a>
                </div>

                <!-- Mobile Menu Button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="w-12 h-12 flex items-center justify-center text-white hover:text-blue-200 hover:bg-white hover:bg-opacity-10 rounded-lg transition-all duration-300 border-2 border-white border-opacity-20">
                        <i id="menu-icon" class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-slate-900 border-t border-blue-500 border-opacity-30 shadow-2xl">
            <div class="px-6 py-6 space-y-2">
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-blue-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-home w-6 text-blue-400 text-lg"></i>
                    <span class="font-medium text-lg">Home</span>
                </a>
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-green-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-chart-line w-6 text-green-400 text-lg"></i>
                    <span class="font-medium text-lg">Investments</span>
                </a>
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-purple-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-newspaper w-6 text-purple-400 text-lg"></i>
                    <span class="font-medium text-lg">Blog</span>
                </a>
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-orange-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-envelope w-6 text-orange-400 text-lg"></i>
                    <span class="font-medium text-lg">Contact</span>
                </a>

                <hr class="my-6 border-gray-600">

                <!-- User Section -->
                <div class="bg-blue-500 bg-opacity-20 rounded-xl p-4 mb-4 border border-blue-500 border-opacity-30">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-lg"></i>
                        </div>
                        <div>
                            <div class="font-bold text-white text-lg">Test User</div>
                            <div class="text-sm text-blue-200">Welcome back!</div>
                        </div>
                    </div>
                </div>

                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-cyan-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-tachometer-alt w-6 text-cyan-400 text-lg"></i>
                    <span class="font-medium text-lg">Dashboard</span>
                </a>
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-white hover:text-yellow-200 hover:bg-white hover:bg-opacity-10 rounded-xl transition-all duration-300">
                    <i class="fas fa-cog w-6 text-yellow-400 text-lg"></i>
                    <span class="font-medium text-lg">Admin Panel</span>
                </a>
                <a href="#" class="mobile-menu-item flex items-center space-x-4 py-4 px-4 text-red-400 hover:text-red-300 hover:bg-red-500 hover:bg-opacity-20 rounded-xl transition-all duration-300">
                    <i class="fas fa-sign-out-alt w-6 text-lg"></i>
                    <span class="font-medium text-lg">Logout</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="pt-20 px-4">
        <div class="max-w-4xl mx-auto">
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h1 class="text-3xl font-bold text-gray-900 mb-6">🧪 Тест мобильной навигации</h1>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                        <h2 class="text-xl font-bold text-blue-900 mb-4">Инструкции для тестирования</h2>
                        <ol class="list-decimal list-inside text-blue-700 space-y-2">
                            <li>Откройте инструменты разработчика (F12)</li>
                            <li>Переключитесь в режим мобильного устройства</li>
                            <li>Нажмите на кнопку меню (☰) в правом верхнем углу</li>
                            <li>Проверьте, что меню имеет темный фон</li>
                            <li>Проверьте, что текст белый и хорошо читается</li>
                            <li>Протестируйте hover эффекты</li>
                        </ol>
                    </div>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                        <h2 class="text-xl font-bold text-green-900 mb-4">Ожидаемое поведение</h2>
                        <ul class="list-disc list-inside text-green-700 space-y-2">
                            <li>Темно-синий/серый фон меню</li>
                            <li>Белый текст с хорошим контрастом</li>
                            <li>Плавная анимация открытия/закрытия</li>
                            <li>Цветные иконки для каждого пункта</li>
                            <li>Hover эффекты с полупрозрачным фоном</li>
                            <li>Автоматическое закрытие при клике вне меню</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                    <h2 class="text-xl font-bold text-yellow-900 mb-4">Проверка на разных размерах экрана</h2>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                        <button onclick="setViewport(320, 568)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                            iPhone SE<br><small>320x568</small>
                        </button>
                        <button onclick="setViewport(375, 667)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                            iPhone 8<br><small>375x667</small>
                        </button>
                        <button onclick="setViewport(414, 896)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                            iPhone 11<br><small>414x896</small>
                        </button>
                        <button onclick="setViewport(768, 1024)" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                            iPad<br><small>768x1024</small>
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="text-center">
                <a href="test_improvements.php" class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Вернуться к тестам
                </a>
            </div>
        </div>
    </main>

    <script>
        // Update debug info
        function updateDebugInfo() {
            document.getElementById('screen-width').textContent = window.innerWidth;
        }

        // Mobile menu toggle with enhanced animation
        document.getElementById('mobile-menu-btn').addEventListener('click', function() {
            const mobileMenu = document.getElementById('mobile-menu');
            const menuIcon = document.getElementById('menu-icon');
            const menuStatus = document.getElementById('menu-status');

            if (mobileMenu.classList.contains('hidden')) {
                mobileMenu.classList.remove('hidden');
                menuIcon.classList.remove('fa-bars');
                menuIcon.classList.add('fa-times');
                menuStatus.textContent = 'Open';
                
                // Enhanced slide down animation
                mobileMenu.style.maxHeight = '0';
                mobileMenu.style.overflow = 'hidden';
                mobileMenu.style.transition = 'max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1)';
                setTimeout(() => {
                    mobileMenu.style.maxHeight = '600px';
                }, 10);
            } else {
                mobileMenu.style.maxHeight = '0';
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
                menuStatus.textContent = 'Closed';
                
                setTimeout(() => {
                    mobileMenu.classList.add('hidden');
                    mobileMenu.style.maxHeight = '';
                    mobileMenu.style.overflow = '';
                    mobileMenu.style.transition = '';
                }, 400);
            }
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');

            if (!mobileMenu.contains(event.target) && !mobileMenuBtn.contains(event.target)) {
                if (!mobileMenu.classList.contains('hidden')) {
                    mobileMenuBtn.click();
                }
            }
        });

        // Close mobile menu on window resize
        window.addEventListener('resize', function() {
            updateDebugInfo();
            
            if (window.innerWidth >= 768) {
                const mobileMenu = document.getElementById('mobile-menu');
                const menuIcon = document.getElementById('menu-icon');
                const menuStatus = document.getElementById('menu-status');
                
                mobileMenu.classList.add('hidden');
                menuIcon.classList.remove('fa-times');
                menuIcon.classList.add('fa-bars');
                menuStatus.textContent = 'Closed';
            }
        });

        // Viewport testing function
        function setViewport(width, height) {
            // This would work in browser dev tools
            alert(`Установите размер экрана в инструментах разработчика: ${width}x${height}px`);
        }

        // Initialize
        updateDebugInfo();
    </script>
</body>
</html>
