<?php
require_once 'config/config.php';
require_once 'includes/functions.php';
require_once 'includes/auth.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тестирование системы верификации</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gradient-to-br from-slate-50 to-blue-50 min-h-screen'>
<div class='container mx-auto px-4 py-8'>";

echo "<div class='max-w-4xl mx-auto'>
        <div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h1 class='text-2xl font-bold text-gray-900 mb-4'>
                <i class='fas fa-vial text-blue-500 mr-2'></i>
                Тестирование системы верификации
            </h1>
            <p class='text-gray-600'>Проверка всех компонентов системы верификации пользователей</p>
        </div>";

try {
    $db = getDB();
    
    // Test 1: Database structure
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 1: Структура базы данных</h2>";
    
    // Check users table
    $stmt = $db->query("DESCRIBE users");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (in_array('verification_status', $columns)) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Поле verification_status в таблице users: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Поле verification_status в таблице users: ✗</p>";
    }
    
    // Check user_verifications table
    $stmt = $db->prepare("SHOW TABLES LIKE 'user_verifications'");
    $stmt->execute();
    
    if ($stmt->fetch()) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Таблица user_verifications: ✓</p>";
        
        // Check table structure
        $stmt = $db->query("DESCRIBE user_verifications");
        $verification_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $required_columns = ['id', 'user_id', 'first_name', 'last_name', 'birth_date', 'passport_photo_path', 'status'];
        
        $missing_columns = array_diff($required_columns, $verification_columns);
        if (empty($missing_columns)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Структура таблицы user_verifications: ✓</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Отсутствуют поля: " . implode(', ', $missing_columns) . "</p>";
        }
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Таблица user_verifications: ✗</p>";
    }
    
    echo "</div>";
    
    // Test 2: Functions
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 2: Функции верификации</h2>";
    
    // Test getUserVerificationStatus
    try {
        $status = getUserVerificationStatus(1);
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>getUserVerificationStatus(): ✓ (статус: $status)</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>getUserVerificationStatus(): ✗ (" . $e->getMessage() . ")</p>";
    }
    
    // Test isUserVerified
    try {
        $verified = isUserVerified(1);
        $verified_text = $verified ? 'да' : 'нет';
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>isUserVerified(): ✓ (верифицирован: $verified_text)</p>";
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>isUserVerified(): ✗ (" . $e->getMessage() . ")</p>";
    }
    
    // Test getUserVerification
    try {
        $verification = getUserVerification(1);
        if ($verification) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>getUserVerification(): ✓ (найдена верификация)</p>";
        } else {
            echo "<p class='text-blue-600'><i class='fas fa-info-circle mr-2'></i>getUserVerification(): ✓ (верификация не найдена - это нормально)</p>";
        }
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>getUserVerification(): ✗ (" . $e->getMessage() . ")</p>";
    }
    
    // Test canInvest with verification check
    try {
        $result = canInvest(1, 100);
        if ($result['success']) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>canInvest(): ✓ (инвестирование разрешено)</p>";
        } else {
            echo "<p class='text-blue-600'><i class='fas fa-info-circle mr-2'></i>canInvest(): ✓ (инвестирование заблокировано: " . $result['message'] . ")</p>";
        }
    } catch (Exception $e) {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>canInvest(): ✗ (" . $e->getMessage() . ")</p>";
    }
    
    echo "</div>";
    
    // Test 3: File structure
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 3: Файловая структура</h2>";
    
    $files_to_check = [
        'pages/verification.php' => 'Страница верификации пользователя',
        'admin/verifications.php' => 'Админ-панель верификаций',
        'admin/verification_details.php' => 'Детали верификации',
        'uploads/verifications' => 'Директория для загрузки паспортов'
    ];
    
    foreach ($files_to_check as $path => $description) {
        if (file_exists($path) || is_dir($path)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>$description: ✓</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>$description: ✗ (не найден: $path)</p>";
        }
    }
    
    echo "</div>";
    
    // Test 4: User interface integration
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 4: Интеграция интерфейса</h2>";
    
    // Check if verification links are added to navigation
    $header_content = file_get_contents('includes/header.php');
    if (strpos($header_content, 'verification.php') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Ссылка на верификацию в навигации: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ссылка на верификацию в навигации: ✗</p>";
    }
    
    // Check if dashboard shows verification status
    $dashboard_content = file_get_contents('pages/dashboard.php');
    if (strpos($dashboard_content, 'verification_status') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Статус верификации в дашборде: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Статус верификации в дашборде: ✗</p>";
    }
    
    // Check if admin panel has verification management
    $admin_index_content = file_get_contents('admin/index.php');
    if (strpos($admin_index_content, 'verifications.php') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Управление верификациями в админ-панели: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Управление верификациями в админ-панели: ✗</p>";
    }
    
    echo "</div>";
    
    // Test 5: Security checks
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест 5: Проверки безопасности</h2>";
    
    // Check if verification pages have CSRF protection
    $verification_content = file_get_contents('pages/verification.php');
    if (strpos($verification_content, 'csrf_token') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>CSRF защита в форме верификации: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>CSRF защита в форме верификации: ✗</p>";
    }
    
    // Check if admin verification page requires admin access
    $admin_verification_content = file_get_contents('admin/verifications.php');
    if (strpos($admin_verification_content, 'requireAdmin()') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Проверка прав администратора: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Проверка прав администратора: ✗</p>";
    }
    
    // Check upload directory permissions
    $upload_dir = 'uploads/verifications';
    if (is_dir($upload_dir) && is_writable($upload_dir)) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Права на запись в директорию загрузок: ✓</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Права на запись в директорию загрузок: ✗</p>";
    }
    
    echo "</div>";
    
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Тестирование завершено!</h2>
            <p class='text-green-700 mb-4'>Система верификации протестирована. Проверьте результаты выше.</p>
            <div class='grid grid-cols-1 md:grid-cols-4 gap-4'>
                <a href='update_verification_system.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-database mr-2'></i>Обновить БД
                </a>
                <a href='pages/verification.php' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-shield-alt mr-2'></i>Верификация
                </a>
                <a href='admin/verifications.php' class='bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='pages/dashboard.php' class='bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-center'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Дашборд
                </a>
            </div>
          </div>";
    
} catch (Exception $e) {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка</h2>
            <p class='text-red-700'>Ошибка тестирования: " . $e->getMessage() . "</p>
          </div>";
}

echo "</div></div></body></html>";
?>
