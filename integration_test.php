<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест интеграции исправлений</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔄 Тест интеграции исправлений</h1>";

$all_passed = true;

// Test 1: File Upload Integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-upload mr-2 text-blue-500'></i>1. Интеграция загрузки файлов</h2>";

// Check if upload functionality works in main dashboard
$dashboard_content = file_get_contents('pages/dashboard.php');

$upload_checks = [
    'Форма имеет enctype="multipart/form-data"' => strpos($dashboard_content, 'enctype="multipart/form-data"') !== false,
    'Поле transaction_screenshot присутствует' => strpos($dashboard_content, 'name="transaction_screenshot"') !== false,
    'Обработка POST запроса присутствует' => strpos($dashboard_content, 'processDepositRequest') !== false,
    'JavaScript обработка файлов присутствует' => strpos($dashboard_content, 'handleFileSelect') !== false,
    'Валидация файлов присутствует' => strpos($dashboard_content, 'allowedTypes') !== false
];

foreach ($upload_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    if (!$result) $all_passed = false;
}

// Test actual upload function
if (function_exists('uploadTransactionScreenshot')) {
    // Create a test image
    $test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    $test_file_path = 'test_integration.png';
    file_put_contents($test_file_path, $test_image_data);
    
    $test_file = [
        'name' => 'test_integration.png',
        'type' => 'image/png',
        'size' => strlen($test_image_data),
        'tmp_name' => $test_file_path,
        'error' => 0
    ];
    
    $upload_result = uploadTransactionScreenshot($test_file);
    
    if ($upload_result['success']) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Функция загрузки работает: " . htmlspecialchars($upload_result['filepath']) . "</p>";
        
        // Clean up
        if (file_exists($upload_result['filepath'])) {
            unlink($upload_result['filepath']);
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка загрузки: " . htmlspecialchars($upload_result['message']) . "</p>";
        $all_passed = false;
    }
    
    // Clean up test file
    if (file_exists($test_file_path)) {
        unlink($test_file_path);
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $all_passed = false;
}

echo "</div>";

// Test 2: Mobile Navigation Integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-mobile-alt mr-2 text-purple-500'></i>2. Интеграция мобильной навигации</h2>";

$header_content = file_get_contents('includes/header.php');

$mobile_checks = [
    'Навигация имеет градиентный фон' => strpos($header_content, 'linear-gradient') !== false,
    'Мобильное меню имеет темный фон' => strpos($header_content, 'bg-slate-900') !== false,
    'Мобильные элементы имеют белый текст' => strpos($header_content, 'text-white') !== false,
    'Кнопка мобильного меню имеет рамку' => strpos($header_content, 'border-white border-opacity-20') !== false,
    'CSS стили для мобильного меню' => strpos($header_content, 'mobile-menu-item') !== false,
    'JavaScript анимации присутствуют' => strpos($header_content, 'cubic-bezier') !== false,
    'Автозакрытие меню реализовано' => strpos($header_content, 'addEventListener(\'click\'') !== false
];

foreach ($mobile_checks as $check => $result) {
    $color = $result ? 'green' : 'red';
    $icon = $result ? 'check' : 'times';
    echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
    if (!$result) $all_passed = false;
}

echo "</div>";

// Test 3: Admin Panel Integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-cog mr-2 text-green-500'></i>3. Интеграция админ-панели</h2>";

if (file_exists('admin/transactions.php')) {
    $admin_content = file_get_contents('admin/transactions.php');
    
    $admin_checks = [
        'Функция просмотра скриншотов' => strpos($admin_content, 'viewScreenshot') !== false,
        'Модальное окно для скриншотов' => strpos($admin_content, 'screenshotModal') !== false,
        'Отображение screenshot_path' => strpos($admin_content, 'screenshot_path') !== false,
        'Кнопка просмотра скриншота' => strpos($admin_content, 'Просмотр') !== false,
        'Обработка отсутствующих скриншотов' => strpos($admin_content, 'Нет скриншота') !== false
    ];
    
    foreach ($admin_checks as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        if (!$result) $all_passed = false;
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Файл admin/transactions.php не найден</p>";
    $all_passed = false;
}

echo "</div>";

// Test 4: Database Integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-database mr-2 text-orange-500'></i>4. Интеграция базы данных</h2>";

try {
    $db = getDB();
    
    // Check transactions table structure
    $stmt = $db->query("DESCRIBE transactions");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    $db_checks = [
        'Таблица transactions существует' => in_array('id', $columns),
        'Колонка screenshot_path существует' => in_array('screenshot_path', $columns),
        'Колонка user_id существует' => in_array('user_id', $columns),
        'Колонка amount существует' => in_array('amount', $columns),
        'Колонка status существует' => in_array('status', $columns)
    ];
    
    foreach ($db_checks as $check => $result) {
        $color = $result ? 'green' : 'red';
        $icon = $result ? 'check' : 'times';
        echo "<p class='text-{$color}-600 mb-2'><i class='fas fa-{$icon} mr-2'></i>$check</p>";
        if (!$result) $all_passed = false;
    }
    
    // Test database connection
    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Подключение к базе данных работает</p>";
    
} catch (Exception $e) {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка базы данных: " . $e->getMessage() . "</p>";
    $all_passed = false;
}

echo "</div>";

// Final Result
if ($all_passed) {
    echo "<div class='bg-green-50 border-2 border-green-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-green-500 mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold text-green-900 mb-4'>🎉 ИНТЕГРАЦИЯ УСПЕШНА!</h2>
            <p class='text-green-700 text-lg mb-6'>Все функции успешно интегрированы в основные файлы проекта!</p>
            
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
                <div class='bg-white rounded-lg p-6 shadow-lg'>
                    <h3 class='text-xl font-bold text-green-900 mb-3'>✅ Загрузка файлов</h3>
                    <ul class='text-green-700 text-left space-y-1'>
                        <li>• Форма в dashboard.php работает</li>
                        <li>• Функции загрузки интегрированы</li>
                        <li>• Валидация файлов активна</li>
                        <li>• Админ-панель отображает скриншоты</li>
                    </ul>
                </div>
                <div class='bg-white rounded-lg p-6 shadow-lg'>
                    <h3 class='text-xl font-bold text-green-900 mb-3'>✅ Мобильная навигация</h3>
                    <ul class='text-green-700 text-left space-y-1'>
                        <li>• Градиентный фон навигации</li>
                        <li>• Темное мобильное меню</li>
                        <li>• Белый текст для контраста</li>
                        <li>• Плавные анимации</li>
                    </ul>
                </div>
            </div>
            
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Тест Dashboard
                </a>
                <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-eye mr-2'></i>Админ-панель
                </a>
                <a href='index.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-mobile-alt mr-2'></i>Тест мобильной навигации
                </a>
                <a href='final_critical_test.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-vial mr-2'></i>Финальные тесты
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>❌ Требуется доработка</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые компоненты требуют дополнительной интеграции. Проверьте детали выше.</p>
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='debug_upload.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-bug mr-2'></i>Отладка загрузки
                </a>
                <a href='test_mobile_nav.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-mobile-alt mr-2'></i>Тест навигации
                </a>
                <a href='fix_database_integrity.php' class='bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-wrench mr-2'></i>Исправить БД
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
