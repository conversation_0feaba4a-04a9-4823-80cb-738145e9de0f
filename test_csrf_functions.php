<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест CSRF функций - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔐 Тест CSRF функций</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            // Test 1: Check if CSRF functions exist
            $csrfFunctions = ['generateCSRFToken', 'verifyCSRFToken'];
            $missingFunctions = [];
            
            foreach ($csrfFunctions as $func) {
                if (!function_exists($func)) {
                    $missingFunctions[] = $func;
                }
            }
            
            if (empty($missingFunctions)) {
                $tests[] = ['name' => 'CSRF функции', 'status' => 'pass', 'message' => 'Все CSRF функции найдены'];
            } else {
                $tests[] = ['name' => 'CSRF функции', 'status' => 'fail', 'message' => 'Отсутствуют функции: ' . implode(', ', $missingFunctions)];
                $allPassed = false;
            }
            
            // Test 2: Test token generation
            try {
                $token1 = generateCSRFToken();
                $token2 = generateCSRFToken();
                
                if (!empty($token1) && !empty($token2) && $token1 === $token2) {
                    $tests[] = ['name' => 'Генерация токенов', 'status' => 'pass', 'message' => 'Токены генерируются корректно и остаются постоянными в сессии'];
                } else {
                    $tests[] = ['name' => 'Генерация токенов', 'status' => 'fail', 'message' => 'Проблема с генерацией токенов'];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Генерация токенов', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 3: Test token verification
            try {
                $validToken = generateCSRFToken();
                $invalidToken = 'invalid_token_123';
                
                $validResult = verifyCSRFToken($validToken);
                $invalidResult = verifyCSRFToken($invalidToken);
                
                if ($validResult === true && $invalidResult === false) {
                    $tests[] = ['name' => 'Проверка токенов', 'status' => 'pass', 'message' => 'Валидация токенов работает корректно'];
                } else {
                    $tests[] = ['name' => 'Проверка токенов', 'status' => 'fail', 'message' => 'Проблема с валидацией токенов'];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Проверка токенов', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 4: Check for old function name usage
            $filesToCheck = [
                'admin/investments.php',
                'pages/dashboard.php',
                'pages/process-investment.php',
                'pages/login.php',
                'pages/register.php',
                'admin/deposits.php',
                'pages/forgot-password.php'
            ];
            
            $filesWithOldFunction = [];
            foreach ($filesToCheck as $file) {
                if (file_exists($file)) {
                    $content = file_get_contents($file);
                    if (strpos($content, 'validateCSRFToken') !== false) {
                        $filesWithOldFunction[] = $file;
                    }
                }
            }
            
            if (empty($filesWithOldFunction)) {
                $tests[] = ['name' => 'Исправление вызовов', 'status' => 'pass', 'message' => 'Все вызовы validateCSRFToken исправлены на verifyCSRFToken'];
            } else {
                $tests[] = ['name' => 'Исправление вызовов', 'status' => 'fail', 'message' => 'Найдены файлы с validateCSRFToken: ' . implode(', ', $filesWithOldFunction)];
                $allPassed = false;
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 'text-red-800';
                $icon = $test['status'] === 'pass' ? '✅' : '❌';
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Show current token info
            if (function_exists('generateCSRFToken')) {
                $currentToken = generateCSRFToken();
                echo '<div class="mb-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">';
                echo '<h3 class="font-semibold text-blue-900 mb-2">🔑 Информация о текущем токене:</h3>';
                echo '<div class="text-sm text-blue-800">';
                echo '<p><strong>Токен:</strong> ' . substr($currentToken, 0, 16) . '... (показаны первые 16 символов)</p>';
                echo '<p><strong>Длина:</strong> ' . strlen($currentToken) . ' символов</p>';
                echo '<p><strong>Сессия ID:</strong> ' . session_id() . '</p>';
                echo '</div>';
                echo '</div>';
            }
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Все CSRF функции работают корректно!' : '⚠️ Обнаружены проблемы с CSRF функциями';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'CSRF защита настроена правильно. Все функции работают и все вызовы исправлены.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы с CSRF функциями.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <!-- Test form -->
            <div class="mb-8 p-6 bg-gray-50 border border-gray-200 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🧪 Тестовая форма CSRF</h3>
                <form method="POST" action="<?php echo $_SERVER['PHP_SELF']; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    <input type="hidden" name="test_action" value="csrf_test">
                    
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded font-medium transition-colors">
                            Тест с валидным токеном
                        </button>
                        <button type="button" onclick="submitInvalidToken()" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded font-medium transition-colors">
                            Тест с невалидным токеном
                        </button>
                    </div>
                </form>
                
                <?php
                if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_action'])) {
                    $testToken = $_POST['csrf_token'] ?? '';
                    $isValid = verifyCSRFToken($testToken);
                    
                    echo '<div class="mt-4 p-3 rounded ' . ($isValid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800') . '">';
                    echo '<strong>Результат теста:</strong> ' . ($isValid ? 'Токен валиден ✅' : 'Токен невалиден ❌');
                    echo '</div>';
                }
                ?>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="admin/investments.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Админ инвестиции
                </a>
                <a href="pages/dashboard.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Дашборд
                </a>
                <a href="pages/login.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Логин
                </a>
                <a href="final_check.php" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Финальная проверка
                </a>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">📋 Исправленные файлы:</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <p>• admin/investments.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• pages/dashboard.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• pages/process-investment.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• pages/login.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• pages/register.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• admin/deposits.php - validateCSRFToken → verifyCSRFToken</p>
                    <p>• pages/forgot-password.php - validateCSRFToken → verifyCSRFToken</p>
                </div>
            </div>
        </div>
    </div>
    
    <script>
    function submitInvalidToken() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '<?php echo $_SERVER['PHP_SELF']; ?>';
        
        const tokenInput = document.createElement('input');
        tokenInput.type = 'hidden';
        tokenInput.name = 'csrf_token';
        tokenInput.value = 'invalid_token_for_testing';
        
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'test_action';
        actionInput.value = 'csrf_test';
        
        form.appendChild(tokenInput);
        form.appendChild(actionInput);
        document.body.appendChild(form);
        form.submit();
    }
    </script>
</body>
</html>
