<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест внешних ключей - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔗 Тест внешних ключей базы данных</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            try {
                $db = getDB();
                
                // Test 1: Check if tables exist
                $tables = ['investments', 'user_investments', 'daily_profits'];
                foreach ($tables as $table) {
                    try {
                        $stmt = $db->query("SELECT COUNT(*) FROM $table");
                        $tests[] = ['name' => "Таблица $table", 'status' => 'pass', 'message' => 'Существует и доступна'];
                    } catch (Exception $e) {
                        $tests[] = ['name' => "Таблица $table", 'status' => 'fail', 'message' => 'Не найдена: ' . $e->getMessage()];
                        $allPassed = false;
                    }
                }
                
                // Test 2: Check foreign key constraints
                $foreignKeyTests = [
                    'user_investments' => ['investment_id -> investments(id)', 'user_id -> users(id)'],
                    'daily_profits' => ['user_investment_id -> user_investments(id)']
                ];
                
                foreach ($foreignKeyTests as $table => $constraints) {
                    try {
                        $stmt = $db->query("SHOW CREATE TABLE $table");
                        $result = $stmt->fetch();
                        $createStatement = $result[1];
                        
                        $foundConstraints = 0;
                        foreach ($constraints as $constraint) {
                            if (strpos($createStatement, 'FOREIGN KEY') !== false) {
                                $foundConstraints++;
                            }
                        }
                        
                        if ($foundConstraints > 0) {
                            $tests[] = ['name' => "FK для $table", 'status' => 'pass', 'message' => "Найдено $foundConstraints внешних ключей"];
                        } else {
                            $tests[] = ['name' => "FK для $table", 'status' => 'fail', 'message' => 'Внешние ключи не найдены'];
                            $allPassed = false;
                        }
                    } catch (Exception $e) {
                        $tests[] = ['name' => "FK для $table", 'status' => 'fail', 'message' => 'Ошибка проверки: ' . $e->getMessage()];
                        $allPassed = false;
                    }
                }
                
                // Test 3: Test referential integrity
                try {
                    // Try to insert a user_investment with non-existent investment_id
                    $stmt = $db->prepare("INSERT INTO user_investments (user_id, investment_id, amount, monthly_rate, start_date, end_date) VALUES (1, 999999, 100.00, 5.00, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 12 MONTH))");
                    $stmt->execute();
                    
                    // If we get here, foreign key constraint is not working
                    $tests[] = ['name' => 'Целостность FK', 'status' => 'fail', 'message' => 'Внешние ключи не работают - вставка недопустимых данных прошла'];
                    $allPassed = false;
                    
                    // Clean up the bad data
                    $db->exec("DELETE FROM user_investments WHERE investment_id = 999999");
                    
                } catch (Exception $e) {
                    // This is expected - foreign key constraint should prevent the insert
                    if (strpos($e->getMessage(), 'foreign key constraint') !== false || strpos($e->getMessage(), 'Cannot add or update') !== false) {
                        $tests[] = ['name' => 'Целостность FK', 'status' => 'pass', 'message' => 'Внешние ключи работают корректно'];
                    } else {
                        $tests[] = ['name' => 'Целостность FK', 'status' => 'warning', 'message' => 'Неожиданная ошибка: ' . $e->getMessage()];
                    }
                }
                
                // Test 4: Check cascade delete
                try {
                    // Get current counts
                    $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                    $investmentCount = $stmt->fetch()['count'];
                    
                    $stmt = $db->query("SELECT COUNT(*) as count FROM user_investments");
                    $userInvestmentCount = $stmt->fetch()['count'];
                    
                    $tests[] = ['name' => 'Подсчет записей', 'status' => 'pass', 'message' => "Инвестиций: $investmentCount, Пользовательских инвестиций: $userInvestmentCount"];
                    
                } catch (Exception $e) {
                    $tests[] = ['name' => 'Подсчет записей', 'status' => 'fail', 'message' => 'Ошибка подсчета: ' . $e->getMessage()];
                    $allPassed = false;
                }
                
            } catch (Exception $e) {
                $tests[] = ['name' => 'Подключение к БД', 'status' => 'fail', 'message' => 'Ошибка подключения: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 
                          ($test['status'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200');
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 
                            ($test['status'] === 'warning' ? 'text-yellow-800' : 'text-red-800');
                $icon = $test['status'] === 'pass' ? '✅' : 
                       ($test['status'] === 'warning' ? '⚠️' : '❌');
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Все тесты внешних ключей пройдены!' : '⚠️ Обнаружены проблемы с внешними ключами';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'База данных настроена корректно. Внешние ключи работают и обеспечивают целостность данных.';
            } else {
                echo 'Пожалуйста, запустите обновление базы данных для исправления проблем с внешними ключами.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="database/update_via_web.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Обновить БД
                </a>
                <a href="final_check.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Финальная проверка
                </a>
                <a href="admin/investments.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Админ-панель
                </a>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">📋 Структура внешних ключей:</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <p><strong>investments</strong> (родительская таблица)</p>
                    <p>↳ <strong>user_investments</strong> → investment_id ссылается на investments(id)</p>
                    <p>&nbsp;&nbsp;&nbsp;&nbsp;↳ <strong>daily_profits</strong> → user_investment_id ссылается на user_investments(id)</p>
                    <p class="mt-2"><em>Все связи настроены с CASCADE DELETE для автоматической очистки связанных данных.</em></p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
