<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

// Redirect if already logged in
if (isLoggedIn()) {
    redirect('../pages/dashboard.php');
}

$page_title = 'Forgot Password';
$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $error_message = 'Invalid security token';
    } else {
        $email = sanitizeInput($_POST['email'] ?? '');
        
        if (empty($email)) {
            $error_message = 'Please enter your email address';
        } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            $error_message = 'Please enter a valid email address';
        } else {
            $token = generatePasswordResetToken($email);
            
            if ($token) {
                // In a real application, you would send an email here
                // For demo purposes, we'll just show a success message
                $success_message = 'If an account with that email exists, we\'ve sent password reset instructions to your email address.';
                
                // Log the reset token for demo purposes
                logError("Password reset token for $email: $token");
            } else {
                // Don't reveal whether the email exists or not for security
                $success_message = 'If an account with that email exists, we\'ve sent password reset instructions to your email address.';
            }
        }
    }
}

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
        <div class="flex justify-center">
            <div class="w-16 h-16 gradient-bg rounded-full flex items-center justify-center">
                <i class="fas fa-anchor text-white text-2xl"></i>
            </div>
        </div>
        <h2 class="mt-6 text-center text-3xl font-bold text-primary">
            Forgot your password?
        </h2>
        <p class="mt-2 text-center text-sm text-gray-800">
            Or
            <a href="login.php" class="font-medium text-light-blue hover:text-primary">
                sign in to your account
            </a>
        </p>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div class="bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10">
            <?php if ($error_message): ?>
                <div class="mb-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                    <?php echo $error_message; ?>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="mb-4 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                    <?php echo $success_message; ?>
                </div>
            <?php endif; ?>

            <?php if (!$success_message): ?>
                <form class="space-y-6" method="POST">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                    
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">
                            Email address
                        </label>
                        <div class="mt-1">
                            <input id="email" name="email" type="email" autocomplete="email" required
                                   value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>"
                                   class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-light-blue focus:border-light-blue sm:text-sm"
                                   placeholder="Enter your email address">
                        </div>
                        <p class="mt-2 text-sm text-gray-700">
                            We'll send password reset instructions to this email address.
                        </p>
                    </div>

                    <div>
                        <button type="submit"
                                class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white btn-primary focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-light-blue">
                            <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                                <i class="fas fa-envelope text-white group-hover:text-gray-200"></i>
                            </span>
                            Send Reset Instructions
                        </button>
                    </div>
                </form>
            <?php else: ?>
                <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-check text-green-600 text-2xl"></i>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-800 mb-2">Check your email</h3>
                    <p class="text-gray-800 mb-6">
                        We've sent password reset instructions to your email address if an account exists.
                    </p>
                    <a href="login.php" class="btn-primary text-white px-6 py-2 rounded-lg font-semibold">
                        Back to Login
                    </a>
                </div>
            <?php endif; ?>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300" />
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="px-2 bg-white text-gray-700">Need help?</span>
                    </div>
                </div>

                <div class="mt-4 text-center">
                    <p class="text-sm text-gray-800 mb-4">
                        If you're having trouble accessing your account, our support team is here to help.
                    </p>
                    <a href="contact.php" class="text-light-blue hover:text-primary font-semibold">
                        Contact Support →
                    </a>
                </div>
            </div>

            <!-- Demo Information -->
            <div class="mt-6 bg-blue-50 p-4 rounded-lg">
                <h4 class="text-sm font-semibold text-blue-800 mb-2">Demo Information</h4>
                <p class="text-xs text-blue-700">
                    In this demo, password reset tokens are logged to the error log file. 
                    In production, these would be sent via email.
                </p>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
