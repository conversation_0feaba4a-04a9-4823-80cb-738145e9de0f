# Система верификации пользователей

## Описание

Система верификации позволяет пользователям подтверждать свою личность для получения доступа к инвестиционным возможностям. Пользователи могут регистрироваться и пополнять счет без верификации, но для инвестирования требуется подтверждение личности администратором.

## Функциональность

### Для пользователей:
- **Регистрация и вход** - доступны без верификации
- **Пополнение счета** - доступно без верификации
- **Инвестирование** - требует верификацию
- **Подача заявки на верификацию** - через страницу `/pages/verification.php`
- **Отслеживание статуса** - в дашборде и на странице верификации

### Для администраторов:
- **Просмотр всех заявок** - через `/admin/verifications.php`
- **Одобрение/отклонение** - с возможностью добавления комментариев
- **Просмотр документов** - с увеличением изображений
- **Статистика** - количество заявок по статусам

## Статусы верификации

1. **unverified** - пользователь не подавал заявку
2. **pending** - заявка подана, ожидает рассмотрения
3. **verified** - заявка одобрена, можно инвестировать
4. **rejected** - заявка отклонена, можно подать новую

## Структура базы данных

### Таблица `users`
Добавлено поле:
- `verification_status` ENUM('unverified', 'pending', 'verified', 'rejected') DEFAULT 'unverified'

### Таблица `user_verifications`
Новая таблица для хранения данных верификации:
- `id` - уникальный идентификатор
- `user_id` - ID пользователя
- `first_name` - имя
- `last_name` - фамилия
- `birth_date` - дата рождения
- `passport_photo_path` - путь к фото паспорта
- `status` - статус заявки
- `admin_notes` - комментарии администратора
- `submitted_at` - дата подачи
- `processed_at` - дата обработки
- `processed_by` - ID администратора

## Файловая структура

### Пользовательские страницы:
- `pages/verification.php` - страница подачи заявки и просмотра статуса

### Административные страницы:
- `admin/verifications.php` - управление заявками
- `admin/verification_details.php` - детальный просмотр заявки

### Директории:
- `uploads/verifications/` - хранение фотографий паспортов

## Установка и настройка

### 1. Обновление базы данных
```bash
# Запустите скрипт обновления
php update_verification_system.php
```

### 2. Проверка системы
```bash
# Запустите тесты
php test_verification_system.php
```

### 3. Настройка прав доступа
Убедитесь, что директория `uploads/verifications/` имеет права на запись (755).

## Использование

### Для пользователей:

1. **Подача заявки:**
   - Перейти на страницу "Verification" в меню
   - Заполнить форму с личными данными
   - Загрузить фото паспорта
   - Отправить заявку

2. **Отслеживание статуса:**
   - Статус отображается в дашборде
   - Детали доступны на странице верификации

### Для администраторов:

1. **Просмотр заявок:**
   - Перейти в админ-панель → "Управление верификациями"
   - Просмотреть список всех заявок

2. **Обработка заявки:**
   - Нажать "Просмотр" для детального изучения
   - Проверить документы и данные
   - Одобрить или отклонить с комментарием

## Безопасность

- CSRF защита всех форм
- Проверка прав доступа
- Валидация загружаемых файлов
- Ограничение размера файлов (5MB)
- Безопасное хранение файлов

## Интеграция

Система интегрирована с:
- Навигационным меню (ссылки на верификацию)
- Дашбордом (уведомления о статусе)
- Системой инвестиций (проверка верификации)
- Админ-панелью (управление заявками)

## Функции API

### Основные функции:
- `getUserVerificationStatus($user_id)` - получить статус верификации
- `isUserVerified($user_id)` - проверить, верифицирован ли пользователь
- `getUserVerification($user_id)` - получить данные верификации
- `submitVerificationRequest()` - подать заявку на верификацию
- `uploadPassportPhoto()` - загрузить фото паспорта

### Обновленные функции:
- `canInvest()` - теперь проверяет статус верификации

## Техническая поддержка

При возникновении проблем:
1. Проверьте права доступа к директориям
2. Убедитесь, что база данных обновлена
3. Запустите тесты системы
4. Проверьте логи ошибок PHP

## Будущие улучшения

Возможные дополнения:
- Email уведомления о смене статуса
- Автоматическая проверка документов
- Интеграция с внешними сервисами верификации
- Массовые операции в админ-панели
- Экспорт отчетов по верификациям
