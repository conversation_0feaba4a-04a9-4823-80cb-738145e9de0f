<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Финальный тест критических исправлений</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔥 Финальный тест критических исправлений</h1>";

$all_passed = true;
$test_results = [];

// Test 1: Transaction Photo Upload System
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-camera mr-2 text-blue-500'></i>1. Система загрузки скриншотов транзакций</h2>";

$upload_tests = [
    'Директория uploads/transactions существует' => is_dir('uploads/transactions'),
    'Директория доступна для записи' => is_writable('uploads/transactions'),
    'Функция uploadTransactionScreenshot существует' => function_exists('uploadTransactionScreenshot'),
    'Функция processDepositRequest существует' => function_exists('processDepositRequest'),
    'Форма загрузки имеет enctype="multipart/form-data"' => strpos(file_get_contents('pages/dashboard.php'), 'enctype="multipart/form-data"') !== false,
    'Поле transaction_screenshot присутствует' => strpos(file_get_contents('pages/dashboard.php'), 'name="transaction_screenshot"') !== false
];

foreach ($upload_tests as $test_name => $result) {
    if ($result) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>$test_name</p>";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>$test_name</p>";
        $all_passed = false;
    }
}

// Check admin panel screenshot viewing
if (file_exists('admin/transactions.php')) {
    $admin_content = file_get_contents('admin/transactions.php');
    $admin_tests = [
        'Функция viewScreenshot присутствует' => strpos($admin_content, 'viewScreenshot') !== false,
        'Модальное окно screenshotModal присутствует' => strpos($admin_content, 'screenshotModal') !== false,
        'Поле screenshot_path используется' => strpos($admin_content, 'screenshot_path') !== false,
        'Кнопка просмотра скриншота присутствует' => strpos($admin_content, 'Просмотр') !== false
    ];
    
    foreach ($admin_tests as $test_name => $result) {
        if ($result) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>$test_name</p>";
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>$test_name</p>";
            $all_passed = false;
        }
    }
}

echo "</div>";

// Test 2: Mobile Header Navigation Design
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-mobile-alt mr-2 text-purple-500'></i>2. Дизайн мобильной навигации</h2>";

if (file_exists('includes/header.php')) {
    $header_content = file_get_contents('includes/header.php');
    
    $mobile_tests = [
        'Мобильное меню имеет темный фон (bg-slate-900)' => strpos($header_content, 'bg-slate-900') !== false,
        'Мобильное меню имеет белый текст (text-white)' => strpos($header_content, 'text-white') !== false,
        'Кнопка мобильного меню имеет белый цвет' => strpos($header_content, 'mobile-menu-btn') !== false,
        'Присутствуют hover эффекты' => strpos($header_content, 'hover:bg-white hover:bg-opacity-10') !== false,
        'Присутствуют анимации' => strpos($header_content, 'transition-all duration-300') !== false,
        'Мобильное меню скрыто по умолчанию' => strpos($header_content, 'md:hidden hidden') !== false,
        'CSS стили для мобильного меню присутствуют' => strpos($header_content, 'mobile-menu-item') !== false
    ];
    
    foreach ($mobile_tests as $test_name => $result) {
        if ($result) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>$test_name</p>";
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>$test_name</p>";
            $all_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл includes/header.php не найден</p>";
    $all_passed = false;
}

echo "</div>";

// Test 3: Database Integrity
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-database mr-2 text-green-500'></i>3. Целостность базы данных</h2>";

try {
    $db = getDB();
    $required_tables = ['users', 'transactions', 'site_settings'];
    
    foreach ($required_tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->fetch()) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Таблица '$table' существует</p>";
            
            // Check if transactions table has screenshot_path column
            if ($table === 'transactions') {
                $stmt = $db->query("DESCRIBE transactions");
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                if (in_array('screenshot_path', $columns)) {
                    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Колонка 'screenshot_path' существует в таблице transactions</p>";
                } else {
                    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Колонка 'screenshot_path' отсутствует в таблице transactions</p>";
                    $all_passed = false;
                }
            }
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Таблица '$table' отсутствует</p>";
            $all_passed = false;
        }
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    $all_passed = false;
}

echo "</div>";

// Test 4: File Upload Functionality Test
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'><i class='fas fa-upload mr-2 text-orange-500'></i>4. Тест функциональности загрузки</h2>";

// Create a test image file
$test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
$test_file_path = 'test_image.png';
file_put_contents($test_file_path, $test_image_data);

// Simulate file upload
$test_file = [
    'name' => 'test_screenshot.png',
    'type' => 'image/png',
    'size' => strlen($test_image_data),
    'tmp_name' => $test_file_path,
    'error' => 0
];

// Test upload function
if (function_exists('uploadTransactionScreenshot')) {
    $upload_result = uploadTransactionScreenshot($test_file);
    
    if ($upload_result['success']) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Функция загрузки работает корректно</p>";
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Файл сохранен: " . htmlspecialchars($upload_result['filepath']) . "</p>";
        
        // Check if file actually exists
        if (file_exists($upload_result['filepath'])) {
            echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Загруженный файл существует на сервере</p>";
            // Clean up test file
            unlink($upload_result['filepath']);
        } else {
            echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Загруженный файл НЕ найден на сервере</p>";
            $all_passed = false;
        }
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка загрузки: " . htmlspecialchars($upload_result['message']) . "</p>";
        $all_passed = false;
    }
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $all_passed = false;
}

// Clean up test file
if (file_exists($test_file_path)) {
    unlink($test_file_path);
}

echo "</div>";

// Final Result
if ($all_passed) {
    echo "<div class='bg-green-50 border-2 border-green-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-green-500 mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold text-green-900 mb-4'>🎉 ВСЕ КРИТИЧЕСКИЕ ПРОБЛЕМЫ ИСПРАВЛЕНЫ!</h2>
            <p class='text-green-700 text-lg mb-6'>Поздравляем! Обе критические проблемы успешно решены:</p>
            <div class='grid grid-cols-1 md:grid-cols-2 gap-6 mb-8'>
                <div class='bg-white rounded-lg p-6 shadow-lg'>
                    <h3 class='text-xl font-bold text-green-900 mb-3'>✅ Система загрузки скриншотов</h3>
                    <ul class='text-green-700 text-left space-y-1'>
                        <li>• Загрузка файлов работает</li>
                        <li>• Файлы сохраняются на сервере</li>
                        <li>• Админ-панель отображает скриншоты</li>
                        <li>• Модальное окно для просмотра</li>
                    </ul>
                </div>
                <div class='bg-white rounded-lg p-6 shadow-lg'>
                    <h3 class='text-xl font-bold text-green-900 mb-3'>✅ Мобильная навигация</h3>
                    <ul class='text-green-700 text-left space-y-1'>
                        <li>• Темный фон меню</li>
                        <li>• Белый текст для контраста</li>
                        <li>• Плавные анимации</li>
                        <li>• Адаптивный дизайн</li>
                    </ul>
                </div>
            </div>
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-tachometer-alt mr-2'></i>Тест загрузки в Dashboard
                </a>
                <a href='test_mobile_nav.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-mobile-alt mr-2'></i>Тест мобильной навигации
                </a>
                <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-eye mr-2'></i>Админ-панель транзакций
                </a>
                <a href='index.php' class='bg-gray-500 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-home mr-2'></i>Главная страница
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>❌ Обнаружены проблемы</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые критические проблемы требуют дополнительного внимания. Проверьте детали выше.</p>
            <div class='flex flex-wrap justify-center gap-4'>
                <a href='fix_database_integrity.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-wrench mr-2'></i>Исправить БД
                </a>
                <a href='test_upload.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-upload mr-2'></i>Тест загрузки
                </a>
                <a href='test_improvements.php' class='bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg transition-colors'>
                    <i class='fas fa-vial mr-2'></i>Полные тесты
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
