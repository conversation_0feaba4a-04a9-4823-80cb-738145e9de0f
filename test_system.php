<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Test - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔧 Poseidon System Test</h1>
            
            <?php
            // Start session first
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }

            require_once 'config/database.php';
            require_once 'includes/functions.php';

            $tests = [];
            $allPassed = true;
            
            // Test 1: Database Connection
            try {
                $db = getDB();
                $tests[] = ['name' => 'Database Connection', 'status' => 'pass', 'message' => 'Successfully connected to database'];
            } catch (Exception $e) {
                $tests[] = ['name' => 'Database Connection', 'status' => 'fail', 'message' => 'Failed: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 2: Investments Table Structure
            try {
                $db = getDB();
                $stmt = $db->query("DESCRIBE investments");
                $columns = $stmt->fetchAll();
                $requiredColumns = ['id', 'title', 'description', 'category', 'min_amount', 'max_amount', 'monthly_rate', 'duration_months', 'image_url', 'is_active'];
                $foundColumns = array_column($columns, 'Field');
                
                $missingColumns = array_diff($requiredColumns, $foundColumns);
                if (empty($missingColumns)) {
                    $tests[] = ['name' => 'Investments Table Structure', 'status' => 'pass', 'message' => 'All required columns present'];
                } else {
                    $tests[] = ['name' => 'Investments Table Structure', 'status' => 'fail', 'message' => 'Missing columns: ' . implode(', ', $missingColumns)];
                    $allPassed = false;
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Investments Table Structure', 'status' => 'fail', 'message' => 'Error: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 3: Sample Investment Data
            try {
                $db = getDB();
                $stmt = $db->query("SELECT COUNT(*) as count FROM investments");
                $result = $stmt->fetch();
                if ($result['count'] > 0) {
                    $tests[] = ['name' => 'Investment Data', 'status' => 'pass', 'message' => $result['count'] . ' investments found'];
                } else {
                    $tests[] = ['name' => 'Investment Data', 'status' => 'warning', 'message' => 'No investment data found'];
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Investment Data', 'status' => 'fail', 'message' => 'Error: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 4: Required Functions
            $requiredFunctions = ['formatCurrency', 'formatPercentage', 'generateCSRFToken', 'verifyCSRFToken', 'setFlashMessage', 'getFlashMessage'];
            $missingFunctions = [];
            foreach ($requiredFunctions as $func) {
                if (!function_exists($func)) {
                    $missingFunctions[] = $func;
                }
            }
            
            if (empty($missingFunctions)) {
                $tests[] = ['name' => 'Required Functions', 'status' => 'pass', 'message' => 'All required functions available'];
            } else {
                $tests[] = ['name' => 'Required Functions', 'status' => 'fail', 'message' => 'Missing functions: ' . implode(', ', $missingFunctions)];
                $allPassed = false;
            }
            
            // Test 5: Users Table
            try {
                $db = getDB();
                $stmt = $db->query("SELECT COUNT(*) as count FROM users");
                $result = $stmt->fetch();
                $tests[] = ['name' => 'Users Table', 'status' => 'pass', 'message' => $result['count'] . ' users found'];
            } catch (Exception $e) {
                $tests[] = ['name' => 'Users Table', 'status' => 'fail', 'message' => 'Error: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Test 6: Admin User
            try {
                $db = getDB();
                $stmt = $db->query("SELECT COUNT(*) as count FROM users WHERE is_admin = 1");
                $result = $stmt->fetch();
                if ($result['count'] > 0) {
                    $tests[] = ['name' => 'Admin User', 'status' => 'pass', 'message' => $result['count'] . ' admin user(s) found'];
                } else {
                    $tests[] = ['name' => 'Admin User', 'status' => 'warning', 'message' => 'No admin users found'];
                }
            } catch (Exception $e) {
                $tests[] = ['name' => 'Admin User', 'status' => 'fail', 'message' => 'Error: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Display results
            echo '<div class="space-y-4">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 
                          ($test['status'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200');
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 
                            ($test['status'] === 'warning' ? 'text-yellow-800' : 'text-red-800');
                $icon = $test['status'] === 'pass' ? '✅' : 
                       ($test['status'] === 'warning' ? '⚠️' : '❌');
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Overall status
            echo '<div class="mt-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 All Critical Tests Passed!' : '⚠️ Some Tests Failed';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'Your Poseidon investment platform is ready to use! All critical components are working correctly.';
            } else {
                echo 'Please fix the failing tests before using the platform. Check the database setup and missing functions.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-4">
                <a href="admin/investments.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Admin Panel
                </a>
                <a href="pages/dashboard.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    User Dashboard
                </a>
                <a href="database/update_via_web.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Update Database
                </a>
            </div>
        </div>
    </div>
</body>
</html>
