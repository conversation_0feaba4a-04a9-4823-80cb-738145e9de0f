<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Тест совместимости данных - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-6xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔍 Тест совместимости данных инвестиций</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            require_once 'config/database.php';
            require_once 'includes/functions.php';
            
            $tests = [];
            $allPassed = true;
            
            try {
                $db = getDB();
                
                // Test 1: Check database columns
                $stmt = $db->query("DESCRIBE investments");
                $columns = $stmt->fetchAll();
                $columnNames = array_column($columns, 'Field');
                
                $requiredColumns = ['id', 'title', 'description', 'category', 'min_amount', 'max_amount', 'monthly_rate', 'duration_months', 'location', 'capital_return', 'features', 'image_url', 'is_active'];
                $missingColumns = array_diff($requiredColumns, $columnNames);
                
                if (empty($missingColumns)) {
                    $tests[] = ['name' => 'Колонки БД', 'status' => 'pass', 'message' => 'Все необходимые колонки присутствуют'];
                } else {
                    $tests[] = ['name' => 'Колонки БД', 'status' => 'fail', 'message' => 'Отсутствуют колонки: ' . implode(', ', $missingColumns)];
                    $allPassed = false;
                }
                
                // Test 2: Check investment data
                $investments = getAllInvestments();
                if (!empty($investments)) {
                    $tests[] = ['name' => 'Данные инвестиций', 'status' => 'pass', 'message' => 'Найдено ' . count($investments) . ' инвестиций'];
                    
                    // Test compatibility fields for first investment
                    $firstInvestment = $investments[0];
                    $compatibilityFields = ['price', 'monthly_rate_min', 'monthly_rate_max', 'return_period_months', 'location', 'capital_return', 'features'];
                    $missingCompatFields = [];
                    
                    foreach ($compatibilityFields as $field) {
                        if (!isset($firstInvestment[$field])) {
                            $missingCompatFields[] = $field;
                        }
                    }
                    
                    if (empty($missingCompatFields)) {
                        $tests[] = ['name' => 'Поля совместимости', 'status' => 'pass', 'message' => 'Все поля совместимости добавлены'];
                    } else {
                        $tests[] = ['name' => 'Поля совместимости', 'status' => 'fail', 'message' => 'Отсутствуют поля: ' . implode(', ', $missingCompatFields)];
                        $allPassed = false;
                    }
                } else {
                    $tests[] = ['name' => 'Данные инвестиций', 'status' => 'fail', 'message' => 'Инвестиции не найдены'];
                    $allPassed = false;
                }
                
                // Test 3: Test specific investment by ID
                if (!empty($investments)) {
                    $testInvestment = getInvestmentById($investments[0]['id']);
                    if ($testInvestment && isset($testInvestment['price']) && isset($testInvestment['monthly_rate_min'])) {
                        $tests[] = ['name' => 'Получение по ID', 'status' => 'pass', 'message' => 'Функция getInvestmentById работает корректно'];
                    } else {
                        $tests[] = ['name' => 'Получение по ID', 'status' => 'fail', 'message' => 'Проблема с функцией getInvestmentById'];
                        $allPassed = false;
                    }
                }
                
            } catch (Exception $e) {
                $tests[] = ['name' => 'Подключение к БД', 'status' => 'fail', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allPassed = false;
            }
            
            // Display results
            echo '<div class="space-y-4 mb-8">';
            foreach ($tests as $test) {
                $bgColor = $test['status'] === 'pass' ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200';
                $textColor = $test['status'] === 'pass' ? 'text-green-800' : 'text-red-800';
                $icon = $test['status'] === 'pass' ? '✅' : '❌';
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $test['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $test['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $test['message'] . '</p>';
                echo '</div>';
            }
            echo '</div>';
            
            // Show sample investment data
            if (!empty($investments)) {
                echo '<div class="mb-8">';
                echo '<h3 class="text-lg font-semibold text-gray-900 mb-4">📊 Пример данных инвестиции:</h3>';
                $sampleInvestment = $investments[0];
                
                echo '<div class="bg-gray-50 border border-gray-200 rounded-lg p-4">';
                echo '<h4 class="font-semibold text-gray-900 mb-3">' . htmlspecialchars($sampleInvestment['title']) . '</h4>';
                echo '<div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">';
                
                $displayFields = [
                    'min_amount' => 'Min Amount',
                    'max_amount' => 'Max Amount', 
                    'price' => 'Price (computed)',
                    'monthly_rate' => 'Monthly Rate',
                    'monthly_rate_min' => 'Rate Min (computed)',
                    'monthly_rate_max' => 'Rate Max (computed)',
                    'duration_months' => 'Duration',
                    'return_period_months' => 'Return Period (computed)',
                    'location' => 'Location',
                    'capital_return' => 'Capital Return',
                    'features' => 'Features'
                ];
                
                foreach ($displayFields as $field => $label) {
                    $value = $sampleInvestment[$field] ?? 'N/A';
                    if (is_bool($value)) {
                        $value = $value ? 'Yes' : 'No';
                    }
                    if (is_numeric($value) && strpos($field, 'amount') !== false) {
                        $value = '$' . number_format($value, 2);
                    }
                    if (is_numeric($value) && strpos($field, 'rate') !== false) {
                        $value = $value . '%';
                    }
                    
                    echo '<div>';
                    echo '<div class="font-medium text-gray-700">' . $label . '</div>';
                    echo '<div class="text-gray-600">' . htmlspecialchars($value) . '</div>';
                    echo '</div>';
                }
                
                echo '</div>';
                echo '</div>';
                echo '</div>';
            }
            
            // Overall status
            echo '<div class="mb-8 p-6 rounded-lg border-2 ' . ($allPassed ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allPassed ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allPassed ? '🎉 Совместимость данных обеспечена!' : '⚠️ Обнаружены проблемы совместимости';
            echo '</h2>';
            echo '<p class="' . ($allPassed ? 'text-green-800' : 'text-red-800') . '">';
            if ($allPassed) {
                echo 'Все поля совместимости добавлены. Старый код будет работать с новой структурой БД.';
            } else {
                echo 'Пожалуйста, обновите базу данных для исправления проблем совместимости.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <a href="database/update_via_web.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Обновить БД
                </a>
                <a href="index.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Главная страница
                </a>
                <a href="pages/investment-detail.php?id=1" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Детали инвестиции
                </a>
                <a href="final_check.php" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                    Финальная проверка
                </a>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">📋 Исправленные поля:</h4>
                <div class="text-sm text-blue-800 space-y-1">
                    <p><strong>price</strong> → min_amount или max_amount</p>
                    <p><strong>monthly_rate_min/max</strong> → monthly_rate (одно значение)</p>
                    <p><strong>return_period_months</strong> → duration_months</p>
                    <p><strong>location</strong> → добавлено в БД</p>
                    <p><strong>capital_return</strong> → добавлено в БД</p>
                    <p><strong>features</strong> → добавлено в БД</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
