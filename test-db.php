<?php
require_once 'config/database.php';

echo "<h1>Database Connection Test</h1>";

try {
    $db = getDB();
    
    if ($db) {
        echo "<p style='color: green;'>✅ Database connection successful!</p>";
        
        // Test query
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM users");
        $stmt->execute();
        $result = $stmt->fetch();
        
        echo "<p>Users in database: " . $result['count'] . "</p>";
        
        // Test investments
        $stmt = $db->prepare("SELECT COUNT(*) as count FROM investments");
        $stmt->execute();
        $result = $stmt->fetch();
        
        echo "<p>Investments in database: " . $result['count'] . "</p>";
        
        // Show sample investment
        $stmt = $db->prepare("SELECT * FROM investments LIMIT 1");
        $stmt->execute();
        $investment = $stmt->fetch();
        
        if ($investment) {
            echo "<h2>Sample Investment:</h2>";
            echo "<pre>" . print_r($investment, true) . "</pre>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database connection failed!</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<h2>PHP Info:</h2>";
echo "<p>PHP Version: " . phpversion() . "</p>";
echo "<p>PDO Available: " . (extension_loaded('pdo') ? 'Yes' : 'No') . "</p>";
echo "<p>PDO MySQL Available: " . (extension_loaded('pdo_mysql') ? 'Yes' : 'No') . "</p>";
?>
