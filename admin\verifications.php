<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$page_title = 'Управление верификациями';

// Handle verification approval/rejection
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (verifyCSRFToken($_POST['csrf_token'] ?? '')) {
        $verification_id = intval($_POST['verification_id'] ?? 0);
        $action = $_POST['action'] ?? '';
        $admin_notes = sanitizeInput($_POST['admin_notes'] ?? '');
        
        if ($verification_id && in_array($action, ['approve', 'reject'])) {
            $db = getDB();
            
            try {
                $db->beginTransaction();
                
                // Get verification details
                $stmt = $db->prepare("SELECT * FROM user_verifications WHERE id = ?");
                $stmt->execute([$verification_id]);
                $verification = $stmt->fetch();
                
                if ($verification) {
                    $new_status = ($action === 'approve') ? 'verified' : 'rejected';
                    $user_verification_status = ($action === 'approve') ? 'verified' : 'rejected';
                    
                    // Update verification record
                    $stmt = $db->prepare("
                        UPDATE user_verifications 
                        SET status = ?, admin_notes = ?, processed_at = NOW(), processed_by = ? 
                        WHERE id = ?
                    ");
                    $stmt->execute([$new_status, $admin_notes, getCurrentUserId(), $verification_id]);
                    
                    // Update user verification status
                    $stmt = $db->prepare("UPDATE users SET verification_status = ? WHERE id = ?");
                    $stmt->execute([$user_verification_status, $verification['user_id']]);
                    
                    $db->commit();
                    
                    $message = ($action === 'approve') ? 'Верификация одобрена' : 'Верификация отклонена';
                    setFlashMessage('success', $message);
                } else {
                    setFlashMessage('error', 'Верификация не найдена');
                }
                
            } catch (Exception $e) {
                $db->rollback();
                logError("Verification processing error: " . $e->getMessage());
                setFlashMessage('error', 'Ошибка при обработке верификации');
            }
        } else {
            setFlashMessage('error', 'Неверные данные');
        }
    } else {
        setFlashMessage('error', 'Неверный токен безопасности');
    }
    
    redirect('verifications.php');
}

// Get all verifications
$db = getDB();
$stmt = $db->query("
    SELECT v.*, u.username, u.email 
    FROM user_verifications v 
    JOIN users u ON v.user_id = u.id 
    ORDER BY v.submitted_at DESC
");
$verifications = $stmt->fetchAll();

// Get statistics
$stats_stmt = $db->query("
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
        SUM(CASE WHEN status = 'verified' THEN 1 ELSE 0 END) as verified,
        SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
    FROM user_verifications
");
$stats = $stats_stmt->fetch();

include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Header -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Управление верификациями</h1>
                <p class="text-gray-600 mt-2">Просмотр и обработка заявок на верификацию пользователей</p>
            </div>
            <a href="index.php" class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-all duration-300">
                <i class="fas fa-arrow-left mr-2"></i>Назад к админ-панели
            </a>
        </div>

        <!-- Flash Messages -->
        <?php displayFlashMessages(); ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-users text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Всего заявок</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['total'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-yellow-500 to-yellow-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-clock text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Ожидают</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['pending'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-check-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Одобрено</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['verified'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl p-6 card-shadow border border-gray-100">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-lg flex items-center justify-center">
                        <i class="fas fa-times-circle text-white text-xl"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm text-gray-600">Отклонено</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $stats['rejected'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Verifications Table -->
        <div class="bg-white rounded-xl card-shadow border border-gray-100">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-xl font-bold text-gray-900">Заявки на верификацию</h2>
            </div>
            
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Пользователь</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Имя</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Дата рождения</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Статус</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Подано</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Действия</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <?php if (empty($verifications)): ?>
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-gray-500">
                                    <i class="fas fa-inbox text-4xl mb-4"></i>
                                    <p>Заявок на верификацию пока нет</p>
                                </td>
                            </tr>
                        <?php else: ?>
                            <?php foreach ($verifications as $verification): ?>
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div>
                                            <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($verification['username']); ?></div>
                                            <div class="text-sm text-gray-500"><?php echo htmlspecialchars($verification['email']); ?></div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($verification['first_name'] . ' ' . $verification['last_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('d.m.Y', strtotime($verification['birth_date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php
                                        $statusColors = [
                                            'pending' => 'bg-yellow-100 text-yellow-800',
                                            'verified' => 'bg-green-100 text-green-800',
                                            'rejected' => 'bg-red-100 text-red-800'
                                        ];
                                        $statusLabels = [
                                            'pending' => 'Ожидает',
                                            'verified' => 'Одобрено',
                                            'rejected' => 'Отклонено'
                                        ];
                                        ?>
                                        <span class="px-2 py-1 text-xs font-medium rounded-full <?php echo $statusColors[$verification['status']]; ?>">
                                            <?php echo $statusLabels[$verification['status']]; ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        <?php echo date('d.m.Y H:i', strtotime($verification['submitted_at'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <button onclick="viewVerification(<?php echo $verification['id']; ?>)" 
                                                class="text-blue-600 hover:text-blue-900 mr-3">
                                            <i class="fas fa-eye mr-1"></i>Просмотр
                                        </button>
                                        <?php if ($verification['status'] === 'pending'): ?>
                                            <button onclick="approveVerification(<?php echo $verification['id']; ?>)" 
                                                    class="text-green-600 hover:text-green-900 mr-3">
                                                <i class="fas fa-check mr-1"></i>Одобрить
                                            </button>
                                            <button onclick="rejectVerification(<?php echo $verification['id']; ?>)" 
                                                    class="text-red-600 hover:text-red-900">
                                                <i class="fas fa-times mr-1"></i>Отклонить
                                            </button>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>

    </div>
</div>

<!-- Verification Details Modal -->
<div id="verificationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-screen overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900">Детали верификации</h3>
                    <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div id="modalContent" class="p-6">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- Action Forms -->
<form id="approveForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="verification_id" id="approve_verification_id">
    <input type="hidden" name="action" value="approve">
    <input type="hidden" name="admin_notes" id="approve_notes">
</form>

<form id="rejectForm" method="POST" style="display: none;">
    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
    <input type="hidden" name="verification_id" id="reject_verification_id">
    <input type="hidden" name="action" value="reject">
    <input type="hidden" name="admin_notes" id="reject_notes">
</form>

<script>
function viewVerification(id) {
    // Load verification details via AJAX
    fetch(`verification_details.php?id=${id}`)
        .then(response => response.text())
        .then(html => {
            document.getElementById('modalContent').innerHTML = html;
            document.getElementById('verificationModal').classList.remove('hidden');
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Ошибка загрузки данных');
        });
}

function closeModal() {
    document.getElementById('verificationModal').classList.add('hidden');
}

function approveVerification(id) {
    const notes = prompt('Комментарий (необязательно):');
    if (notes !== null) {
        document.getElementById('approve_verification_id').value = id;
        document.getElementById('approve_notes').value = notes;
        document.getElementById('approveForm').submit();
    }
}

function rejectVerification(id) {
    const notes = prompt('Причина отклонения:');
    if (notes !== null && notes.trim() !== '') {
        document.getElementById('reject_verification_id').value = id;
        document.getElementById('reject_notes').value = notes;
        document.getElementById('rejectForm').submit();
    } else if (notes !== null) {
        alert('Пожалуйста, укажите причину отклонения');
    }
}

// Close modal when clicking outside
document.getElementById('verificationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});
</script>

<?php include '../includes/footer.php'; ?>
