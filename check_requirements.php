<?php
/**
 * EcoGenix System Requirements Checker
 * Проверка системных требований перед установкой
 */

function checkRequirement($name, $condition, $required = true) {
    $status = $condition ? '✅' : ($required ? '❌' : '⚠️');
    $class = $condition ? 'success' : ($required ? 'error' : 'warning');
    return [
        'name' => $name,
        'status' => $status,
        'passed' => $condition,
        'required' => $required,
        'class' => $class
    ];
}

// Проверка требований
$requirements = [
    checkRequirement('PHP версия 7.4+', version_compare(PHP_VERSION, '7.4.0', '>=')),
    checkRequirement('PDO расширение', extension_loaded('pdo')),
    checkRequirement('PDO MySQL драйвер', extension_loaded('pdo_mysql')),
    checkRequirement('JSON расширение', extension_loaded('json')),
    checkRequirement('mbstring расширение', extension_loaded('mbstring')),
    checkRequirement('OpenSSL расширение', extension_loaded('openssl')),
    checkRequirement('cURL расширение', extension_loaded('curl'), false),
    checkRequirement('GD расширение', extension_loaded('gd'), false),
];

// Проверка файлов и папок
$file_checks = [
    checkRequirement('Файл database/schema.sql', file_exists(__DIR__ . '/database/schema.sql')),
    checkRequirement('Папка config/ доступна для записи', is_writable(__DIR__ . '/config')),
    checkRequirement('Файл config/database.php', file_exists(__DIR__ . '/config/database.php')),
    checkRequirement('Файл includes/functions.php', file_exists(__DIR__ . '/includes/functions.php')),
];

$all_required_passed = true;
foreach (array_merge($requirements, $file_checks) as $check) {
    if ($check['required'] && !$check['passed']) {
        $all_required_passed = false;
        break;
    }
}
?>
<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Проверка требований - EcoGenix</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }
        
        .content {
            padding: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #374151;
            margin-bottom: 15px;
            font-size: 1.5rem;
        }
        
        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            margin-bottom: 8px;
            border-radius: 10px;
            border-left: 4px solid;
        }
        
        .requirement.success {
            background: #d1fae5;
            border-left-color: #10b981;
        }
        
        .requirement.error {
            background: #fee2e2;
            border-left-color: #ef4444;
        }
        
        .requirement.warning {
            background: #fef3c7;
            border-left-color: #f59e0b;
        }
        
        .requirement-name {
            font-weight: 600;
            color: #374151;
        }
        
        .requirement-status {
            font-size: 1.2rem;
        }
        
        .system-info {
            background: #f9fafb;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .system-info h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .info-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        
        .info-label {
            font-weight: 600;
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .info-value {
            color: #374151;
            font-size: 1.1rem;
        }
        
        .actions {
            text-align: center;
            padding: 20px 0;
        }
        
        .btn {
            display: inline-block;
            padding: 15px 30px;
            border-radius: 10px;
            text-decoration: none;
            font-weight: 600;
            margin: 0 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #059669 0%, #10b981 100%);
            color: white;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .alert {
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d1fae5;
            border: 1px solid #10b981;
            color: #065f46;
        }
        
        .alert-error {
            background: #fee2e2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌱 EcoGenix</h1>
            <p>Проверка системных требований</p>
        </div>
        
        <div class="content">
            <?php if ($all_required_passed): ?>
                <div class="alert alert-success">
                    <strong>✅ Все требования выполнены!</strong><br>
                    Система готова для установки EcoGenix.
                </div>
            <?php else: ?>
                <div class="alert alert-error">
                    <strong>❌ Некоторые требования не выполнены!</strong><br>
                    Пожалуйста, устраните проблемы перед установкой.
                </div>
            <?php endif; ?>
            
            <div class="system-info">
                <h3>📊 Информация о системе</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <div class="info-label">PHP версия</div>
                        <div class="info-value"><?php echo PHP_VERSION; ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Операционная система</div>
                        <div class="info-value"><?php echo PHP_OS; ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Веб-сервер</div>
                        <div class="info-value"><?php echo $_SERVER['SERVER_SOFTWARE'] ?? 'Неизвестно'; ?></div>
                    </div>
                    <div class="info-item">
                        <div class="info-label">Максимальный размер файла</div>
                        <div class="info-value"><?php echo ini_get('upload_max_filesize'); ?></div>
                    </div>
                </div>
            </div>
            
            <div class="section">
                <h2>🔧 PHP расширения</h2>
                <?php foreach ($requirements as $req): ?>
                    <div class="requirement <?php echo $req['class']; ?>">
                        <span class="requirement-name"><?php echo $req['name']; ?></span>
                        <span class="requirement-status"><?php echo $req['status']; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="section">
                <h2>📁 Файлы и папки</h2>
                <?php foreach ($file_checks as $check): ?>
                    <div class="requirement <?php echo $check['class']; ?>">
                        <span class="requirement-name"><?php echo $check['name']; ?></span>
                        <span class="requirement-status"><?php echo $check['status']; ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
            
            <div class="actions">
                <?php if ($all_required_passed): ?>
                    <a href="install.php" class="btn btn-primary">🚀 Перейти к установке</a>
                <?php else: ?>
                    <a href="#" class="btn btn-primary" style="opacity: 0.5; cursor: not-allowed;">🚀 Установка недоступна</a>
                <?php endif; ?>
                <a href="javascript:location.reload()" class="btn btn-secondary">🔄 Проверить снова</a>
            </div>
        </div>
    </div>
</body>
</html>
