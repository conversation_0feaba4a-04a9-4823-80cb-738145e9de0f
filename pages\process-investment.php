<?php
require_once '../config/config.php';
require_once '../includes/auth.php';

requireLogin();

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    redirect('investments.php');
}

if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    setFlashMessage('error', 'Invalid security token');
    redirect('investments.php');
}

$user_id = getCurrentUserId();
$investment_id = intval($_POST['investment_id'] ?? 0);
$amount = floatval($_POST['amount'] ?? 0);

if (!$investment_id || !$amount) {
    setFlashMessage('error', 'Invalid investment data');
    redirect('investments.php');
}

$result = processInvestment($user_id, $investment_id, $amount);

if ($result['success']) {
    setFlashMessage('success', $result['message']);
    redirect('dashboard.php');
} else {
    setFlashMessage('error', $result['message']);
    redirect('investment-detail.php?id=' . $investment_id);
}
?>
