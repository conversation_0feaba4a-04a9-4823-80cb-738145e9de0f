<?php
/**
 * Тестовый файл для демонстрации отображения адресов кошельков в админке
 * Этот файл показывает, как теперь работает система транзакций
 */

require_once 'config/config.php';
require_once 'includes/functions.php';

echo "<h1>Тест отображения адресов кошельков в админке</h1>";

// Симуляция данных транзакций для демонстрации
$test_transactions = [
    [
        'id' => 1,
        'type' => 'deposit',
        'amount' => 100.00,
        'username' => 'testuser1',
        'email' => '<EMAIL>',
        'screenshot_path' => 'uploads/transactions/screenshot_123.jpg',
        'wallet_address' => null,
        'status' => 'pending',
        'created_at' => '2025-06-16 10:00:00'
    ],
    [
        'id' => 2,
        'type' => 'withdrawal',
        'amount' => 50.00,
        'username' => 'testuser2',
        'email' => '<EMAIL>',
        'screenshot_path' => null,
        'wallet_address' => 'TQn9Y2khEsLJW1ChVWFMSMeRDow5KcbLSE',
        'status' => 'pending',
        'created_at' => '2025-06-16 11:00:00'
    ],
    [
        'id' => 3,
        'type' => 'withdrawal',
        'amount' => 75.00,
        'username' => 'testuser3',
        'email' => '<EMAIL>',
        'screenshot_path' => null,
        'wallet_address' => 'TLyqzVGLV1srkB7dToTAEqgDSfPtXRJZYH',
        'status' => 'approved',
        'created_at' => '2025-06-16 12:00:00'
    ]
];

echo "<h2>Демонстрация отображения транзакций:</h2>";
echo "<table border='1' cellpadding='10' style='border-collapse: collapse; width: 100%;'>";
echo "<tr style='background-color: #f0f0f0;'>";
echo "<th>ID</th><th>Тип</th><th>Пользователь</th><th>Сумма</th><th>Скриншот/Адрес</th><th>Статус</th>";
echo "</tr>";

foreach ($test_transactions as $transaction) {
    echo "<tr>";
    echo "<td>{$transaction['id']}</td>";
    echo "<td>" . ($transaction['type'] === 'deposit' ? 'Депозит' : 'Вывод') . "</td>";
    echo "<td>{$transaction['username']}<br><small>{$transaction['email']}</small></td>";
    echo "<td>" . formatCurrency($transaction['amount']) . "</td>";
    echo "<td>";
    
    if ($transaction['type'] === 'withdrawal') {
        // Для выводов показываем адрес кошелька
        if (!empty($transaction['wallet_address'])) {
            echo "<div style='max-width: 200px;'>";
            echo "<div style='font-size: 11px; color: #666; margin-bottom: 5px;'>Адрес кошелька:</div>";
            echo "<div style='background-color: #f5f5f5; padding: 8px; border-radius: 4px; font-family: monospace; font-size: 12px; word-break: break-all; border: 1px solid #ddd;'>";
            echo htmlspecialchars($transaction['wallet_address']);
            echo "</div>";
            echo "<button style='margin-top: 5px; background-color: #007cba; color: white; padding: 4px 8px; border: none; border-radius: 4px; font-size: 11px; cursor: pointer;'>";
            echo "📋 Копировать";
            echo "</button>";
            echo "</div>";
        } else {
            echo "<span style='color: #dc3545; font-size: 12px;'>⚠️ Адрес не указан</span>";
        }
    } else {
        // Для депозитов показываем скриншот
        if (!empty($transaction['screenshot_path'])) {
            echo "<button style='background-color: #007cba; color: white; padding: 6px 12px; border: none; border-radius: 4px; font-size: 12px; cursor: pointer;'>";
            echo "🖼️ Просмотр скриншота";
            echo "</button>";
        } else {
            echo "<span style='color: #6c757d; font-size: 12px;'>Нет скриншота</span>";
        }
    }
    
    echo "</td>";
    echo "<td>";
    if ($transaction['status'] === 'pending') {
        echo "<span style='background-color: #ffc107; color: #000; padding: 4px 8px; border-radius: 4px; font-size: 11px;'>Ожидает</span>";
    } elseif ($transaction['status'] === 'approved') {
        echo "<span style='background-color: #28a745; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;'>Одобрено</span>";
    } else {
        echo "<span style='background-color: #dc3545; color: white; padding: 4px 8px; border-radius: 4px; font-size: 11px;'>Отклонено</span>";
    }
    echo "</td>";
    echo "</tr>";
}

echo "</table>";

echo "<h2>Ключевые изменения в системе:</h2>";
echo "<ul>";
echo "<li>✅ <strong>Заголовок колонки изменен</strong> с 'Скриншот' на 'Скриншот/Адрес'</li>";
echo "<li>✅ <strong>Для депозитов</strong> отображается кнопка просмотра скриншота</li>";
echo "<li>✅ <strong>Для выводов</strong> отображается полный адрес кошелька в удобном формате</li>";
echo "<li>✅ <strong>Адрес кошелька</strong> можно скопировать одним кликом</li>";
echo "<li>✅ <strong>Адрес отображается</strong> в моноширинном шрифте для лучшей читаемости</li>";
echo "<li>✅ <strong>Предупреждение</strong> если адрес кошелька не указан</li>";
echo "</ul>";

echo "<h2>Файлы, которые были изменены:</h2>";
echo "<ul>";
echo "<li><code>admin/transactions.php</code> - основная логика отображения</li>";
echo "<li><code>admin/withdrawals.php</code> - исправлена функция CSRF</li>";
echo "</ul>";

echo "<h2>Структура базы данных:</h2>";
echo "<p>Таблица <code>transactions</code> уже содержит поле <code>wallet_address</code> для хранения адресов кошельков при выводах.</p>";

echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo "h1 { color: #333; }";
echo "h2 { color: #666; margin-top: 30px; }";
echo "table { margin: 20px 0; }";
echo "ul { margin: 10px 0; }";
echo "li { margin: 5px 0; }";
echo "code { background-color: #f5f5f5; padding: 2px 4px; border-radius: 3px; }";
echo "</style>";
?>
