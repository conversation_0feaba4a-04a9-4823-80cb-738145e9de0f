<?php
require_once '../config/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$verification_id = intval($_GET['id'] ?? 0);

if (!$verification_id) {
    echo '<p class="text-red-600">Неверный ID верификации</p>';
    exit;
}

$db = getDB();
$stmt = $db->prepare("
    SELECT v.*, u.username, u.email, u.created_at as user_created,
           admin.username as processed_by_username
    FROM user_verifications v 
    JOIN users u ON v.user_id = u.id 
    LEFT JOIN users admin ON v.processed_by = admin.id
    WHERE v.id = ?
");
$stmt->execute([$verification_id]);
$verification = $stmt->fetch();

if (!$verification) {
    echo '<p class="text-red-600">Верификация не найдена</p>';
    exit;
}
?>

<div class="space-y-6">
    <!-- User Information -->
    <div>
        <h4 class="text-lg font-medium text-gray-900 mb-3">Информация о пользователе</h4>
        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
            <div class="flex justify-between">
                <span class="text-gray-600">Имя пользователя:</span>
                <span class="font-medium"><?php echo htmlspecialchars($verification['username']); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Email:</span>
                <span class="font-medium"><?php echo htmlspecialchars($verification['email']); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Регистрация:</span>
                <span class="font-medium"><?php echo date('d.m.Y H:i', strtotime($verification['user_created'])); ?></span>
            </div>
        </div>
    </div>

    <!-- Verification Details -->
    <div>
        <h4 class="text-lg font-medium text-gray-900 mb-3">Данные верификации</h4>
        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
            <div class="flex justify-between">
                <span class="text-gray-600">Имя:</span>
                <span class="font-medium"><?php echo htmlspecialchars($verification['first_name']); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Фамилия:</span>
                <span class="font-medium"><?php echo htmlspecialchars($verification['last_name']); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Дата рождения:</span>
                <span class="font-medium"><?php echo date('d.m.Y', strtotime($verification['birth_date'])); ?></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Возраст:</span>
                <span class="font-medium">
                    <?php 
                    $birthDate = new DateTime($verification['birth_date']);
                    $today = new DateTime();
                    $age = $today->diff($birthDate)->y;
                    echo $age . ' лет';
                    ?>
                </span>
            </div>
        </div>
    </div>

    <!-- Passport Photo -->
    <div>
        <h4 class="text-lg font-medium text-gray-900 mb-3">Фотография паспорта</h4>
        <div class="border border-gray-200 rounded-lg p-4">
            <?php if (file_exists('../' . $verification['passport_photo_path'])): ?>
                <div class="text-center">
                    <img src="../<?php echo htmlspecialchars($verification['passport_photo_path']); ?>" 
                         alt="Passport Photo" 
                         class="max-w-full max-h-96 mx-auto rounded-lg shadow-lg cursor-pointer"
                         onclick="openImageModal(this.src)">
                    <p class="text-sm text-gray-500 mt-2">Нажмите на изображение для увеличения</p>
                </div>
            <?php else: ?>
                <div class="text-center text-red-600">
                    <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                    <p>Файл изображения не найден</p>
                    <p class="text-sm text-gray-500"><?php echo htmlspecialchars($verification['passport_photo_path']); ?></p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Status Information -->
    <div>
        <h4 class="text-lg font-medium text-gray-900 mb-3">Статус верификации</h4>
        <div class="bg-gray-50 rounded-lg p-4 space-y-2">
            <div class="flex justify-between">
                <span class="text-gray-600">Текущий статус:</span>
                <span class="font-medium">
                    <?php
                    $statusLabels = [
                        'pending' => 'Ожидает рассмотрения',
                        'verified' => 'Одобрено',
                        'rejected' => 'Отклонено'
                    ];
                    $statusColors = [
                        'pending' => 'text-yellow-600',
                        'verified' => 'text-green-600',
                        'rejected' => 'text-red-600'
                    ];
                    ?>
                    <span class="<?php echo $statusColors[$verification['status']]; ?>">
                        <?php echo $statusLabels[$verification['status']]; ?>
                    </span>
                </span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Подано:</span>
                <span class="font-medium"><?php echo date('d.m.Y H:i', strtotime($verification['submitted_at'])); ?></span>
            </div>
            <?php if ($verification['processed_at']): ?>
            <div class="flex justify-between">
                <span class="text-gray-600">Обработано:</span>
                <span class="font-medium"><?php echo date('d.m.Y H:i', strtotime($verification['processed_at'])); ?></span>
            </div>
            <?php endif; ?>
            <?php if ($verification['processed_by_username']): ?>
            <div class="flex justify-between">
                <span class="text-gray-600">Обработал:</span>
                <span class="font-medium"><?php echo htmlspecialchars($verification['processed_by_username']); ?></span>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Admin Notes -->
    <?php if ($verification['admin_notes']): ?>
    <div>
        <h4 class="text-lg font-medium text-gray-900 mb-3">Комментарии администратора</h4>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <p class="text-blue-800"><?php echo nl2br(htmlspecialchars($verification['admin_notes'])); ?></p>
        </div>
    </div>
    <?php endif; ?>

    <!-- Action Buttons -->
    <?php if ($verification['status'] === 'pending'): ?>
    <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button onclick="closeModal(); approveVerification(<?php echo $verification['id']; ?>)" 
                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium">
            <i class="fas fa-check mr-2"></i>Одобрить
        </button>
        <button onclick="closeModal(); rejectVerification(<?php echo $verification['id']; ?>)" 
                class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg font-medium">
            <i class="fas fa-times mr-2"></i>Отклонить
        </button>
    </div>
    <?php endif; ?>
</div>

<!-- Image Modal -->
<div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 hidden z-50" onclick="closeImageModal()">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="relative max-w-4xl max-h-full">
            <img id="modalImage" src="" alt="Enlarged passport photo" class="max-w-full max-h-full">
            <button onclick="closeImageModal()" 
                    class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</div>

<script>
function openImageModal(src) {
    document.getElementById('modalImage').src = src;
    document.getElementById('imageModal').classList.remove('hidden');
}

function closeImageModal() {
    document.getElementById('imageModal').classList.add('hidden');
}
</script>
