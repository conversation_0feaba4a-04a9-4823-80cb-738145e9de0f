/* Mobile-specific styles for Poseidon Rental Pro */

/* Mobile Navigation Enhancements */
@media (max-width: 768px) {
    .navbar-glass {
        backdrop-filter: blur(15px);
        background: rgba(255, 255, 255, 0.95);
    }
    
    /* Hero Section Mobile */
    .hero-section {
        min-height: 100vh;
        padding: 2rem 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
        margin-bottom: 1.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.125rem;
        margin-bottom: 2rem;
    }
    
    /* Stats Grid Mobile */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
    }
    
    /* Features Mobile */
    .features-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .feature-card {
        padding: 1.5rem;
    }
    
    /* Investment Cards Mobile */
    .investment-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .investment-card {
        margin-bottom: 1rem;
    }
    
    /* Blog Cards Mobile */
    .blog-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    /* Footer Mobile */
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-newsletter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .footer-newsletter input {
        margin-bottom: 1rem;
        border-radius: 0.75rem;
    }
    
    .footer-newsletter button {
        border-radius: 0.75rem;
    }
    
    /* Button Improvements */
    .btn-primary,
    .btn-secondary {
        padding: 1rem 1.5rem;
        font-size: 1rem;
        min-height: 48px; /* Touch target size */
    }
    
    /* Form Improvements */
    input[type="email"],
    input[type="text"],
    input[type="password"],
    textarea {
        min-height: 48px;
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    /* Card Hover Effects - Reduced for mobile */
    .card-hover:hover {
        transform: translateY(-4px);
    }
    
    /* Floating Elements - Smaller on mobile */
    .animate-float {
        animation-duration: 8s;
    }
    
    /* Text Sizes */
    .text-5xl {
        font-size: 2.5rem;
    }
    
    .text-6xl {
        font-size: 3rem;
    }
    
    .text-7xl {
        font-size: 3.5rem;
    }
    
    /* Spacing Adjustments */
    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
    
    .py-20 {
        padding-top: 2.5rem;
        padding-bottom: 2.5rem;
    }
    
    /* Mobile Menu Enhancements */
    #mobile-menu {
        backdrop-filter: blur(20px);
        background: rgba(255, 255, 255, 0.98);
    }
    
    /* Flash Messages Mobile */
    .flash-message {
        position: fixed;
        top: 5rem;
        left: 1rem;
        right: 1rem;
        max-width: none;
        z-index: 60;
    }
}

/* Tablet Styles */
@media (min-width: 769px) and (max-width: 1024px) {
    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .investment-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .blog-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .card-hover:hover {
        transform: none;
    }
    
    .btn-primary:hover,
    .btn-secondary:hover {
        transform: none;
    }
    
    /* Increase touch targets */
    button,
    .btn-primary,
    .btn-secondary,
    a[role="button"] {
        min-height: 44px;
        min-width: 44px;
    }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .hero-pattern {
        background-size: 50px 50px;
    }
}

/* Landscape Mobile */
@media (max-width: 768px) and (orientation: landscape) {
    .hero-section {
        min-height: 80vh;
    }
    
    .hero-title {
        font-size: 2rem;
    }
    
    .py-24 {
        padding-top: 2rem;
        padding-bottom: 2rem;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .navbar-glass {
        background: rgba(15, 23, 42, 0.95);
        border-bottom: 1px solid rgba(59, 130, 246, 0.2);
    }
    
    #mobile-menu {
        background: rgba(15, 23, 42, 0.98);
    }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    .animate-float,
    .animate-pulse-slow {
        animation: none;
    }
    
    .card-hover {
        transition: none;
    }
    
    * {
        transition-duration: 0.01ms !important;
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
    }
}

/* Print Styles */
@media print {
    .navbar-glass,
    #mobile-menu,
    .flash-message,
    .animate-float {
        display: none !important;
    }
    
    .gradient-primary,
    .gradient-secondary {
        background: #1e40af !important;
        color: white !important;
    }
}
