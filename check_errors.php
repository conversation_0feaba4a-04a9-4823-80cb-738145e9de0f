<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Проверка ошибок - Poseidon</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-6">🔍 Проверка исправления ошибок</h1>
            
            <?php
            // Start session
            if (session_status() == PHP_SESSION_NONE) {
                session_start();
            }
            
            $checks = [];
            $allGood = true;
            
            // Check 1: Include files without errors
            echo '<div class="space-y-4">';
            
            try {
                require_once 'config/database.php';
                $checks[] = ['name' => 'Database Config', 'status' => 'success', 'message' => 'Загружен без ошибок'];
            } catch (Exception $e) {
                $checks[] = ['name' => 'Database Config', 'status' => 'error', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allGood = false;
            }
            
            try {
                require_once 'includes/functions.php';
                $checks[] = ['name' => 'Functions File', 'status' => 'success', 'message' => 'Загружен без ошибок'];
            } catch (Exception $e) {
                $checks[] = ['name' => 'Functions File', 'status' => 'error', 'message' => 'Ошибка: ' . $e->getMessage()];
                $allGood = false;
            }
            
            // Check 2: Function availability
            $functions = ['formatCurrency', 'formatPercentage', 'generateCSRFToken', 'verifyCSRFToken', 'setFlashMessage', 'getFlashMessage', 'redirect'];
            $missingFunctions = [];

            foreach ($functions as $func) {
                if (!function_exists($func)) {
                    $missingFunctions[] = $func;
                }
            }

            if (empty($missingFunctions)) {
                $checks[] = ['name' => 'Required Functions', 'status' => 'success', 'message' => 'Все необходимые функции доступны (включая redirect)'];
            } else {
                $checks[] = ['name' => 'Required Functions', 'status' => 'error', 'message' => 'Отсутствуют функции: ' . implode(', ', $missingFunctions)];
                $allGood = false;
            }
            
            // Check 3: Database connection
            try {
                $db = getDB();
                $checks[] = ['name' => 'Database Connection', 'status' => 'success', 'message' => 'Подключение к базе данных успешно'];
            } catch (Exception $e) {
                $checks[] = ['name' => 'Database Connection', 'status' => 'error', 'message' => 'Ошибка подключения: ' . $e->getMessage()];
                $allGood = false;
            }
            
            // Check 4: Test function calls
            try {
                $testAmount = formatCurrency(1234.56);
                $testPercent = formatPercentage(12.34);
                $testToken = generateCSRFToken();

                if ($testAmount === '$1,234.56' && $testPercent === '12.34' && !empty($testToken)) {
                    $checks[] = ['name' => 'Function Calls', 'status' => 'success', 'message' => 'Все функции работают корректно'];
                } else {
                    $checks[] = ['name' => 'Function Calls', 'status' => 'warning', 'message' => 'Функции работают, но результаты могут отличаться'];
                }
            } catch (Exception $e) {
                $checks[] = ['name' => 'Function Calls', 'status' => 'error', 'message' => 'Ошибка вызова функций: ' . $e->getMessage()];
                $allGood = false;
            }

            // Check 5: Redirect function specifically
            try {
                if (function_exists('redirect')) {
                    $checks[] = ['name' => 'Redirect Function', 'status' => 'success', 'message' => 'Функция redirect() доступна и не дублируется'];
                } else {
                    $checks[] = ['name' => 'Redirect Function', 'status' => 'error', 'message' => 'Функция redirect() не найдена'];
                    $allGood = false;
                }
            } catch (Exception $e) {
                $checks[] = ['name' => 'Redirect Function', 'status' => 'error', 'message' => 'Ошибка проверки redirect(): ' . $e->getMessage()];
                $allGood = false;
            }
            
            // Display results
            foreach ($checks as $check) {
                $bgColor = $check['status'] === 'success' ? 'bg-green-50 border-green-200' : 
                          ($check['status'] === 'warning' ? 'bg-yellow-50 border-yellow-200' : 'bg-red-50 border-red-200');
                $textColor = $check['status'] === 'success' ? 'text-green-800' : 
                            ($check['status'] === 'warning' ? 'text-yellow-800' : 'text-red-800');
                $icon = $check['status'] === 'success' ? '✅' : 
                       ($check['status'] === 'warning' ? '⚠️' : '❌');
                
                echo '<div class="' . $bgColor . ' border rounded-lg p-4">';
                echo '<div class="flex items-center justify-between">';
                echo '<h3 class="font-semibold ' . $textColor . '">' . $icon . ' ' . $check['name'] . '</h3>';
                echo '<span class="text-sm ' . $textColor . ' uppercase font-medium">' . $check['status'] . '</span>';
                echo '</div>';
                echo '<p class="text-sm ' . $textColor . ' mt-1">' . $check['message'] . '</p>';
                echo '</div>';
            }
            
            echo '</div>';
            
            // Overall status
            echo '<div class="mt-8 p-6 rounded-lg border-2 ' . ($allGood ? 'bg-green-50 border-green-300' : 'bg-red-50 border-red-300') . '">';
            echo '<h2 class="text-xl font-bold ' . ($allGood ? 'text-green-900' : 'text-red-900') . ' mb-2">';
            echo $allGood ? '🎉 Все ошибки исправлены!' : '⚠️ Обнаружены проблемы';
            echo '</h2>';
            echo '<p class="' . ($allGood ? 'text-green-800' : 'text-red-800') . '">';
            if ($allGood) {
                echo 'Отлично! Все критические ошибки PHP исправлены. Платформа Poseidon готова к использованию.';
            } else {
                echo 'Пожалуйста, исправьте обнаруженные проблемы перед использованием платформы.';
            }
            echo '</p>';
            echo '</div>';
            ?>
            
            <div class="mt-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">🧪 Тестирование страниц</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="test_system.php" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        Системный тест
                    </a>
                    <a href="database/update_via_web.php" class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        Обновление БД
                    </a>
                    <a href="admin/investments.php" class="bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        Админ-панель
                    </a>
                    <a href="pages/dashboard.php" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-3 rounded-lg text-center font-medium transition-colors">
                        Дашборд
                    </a>
                </div>
            </div>
            
            <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <h4 class="font-semibold text-blue-900 mb-2">📋 Что было исправлено:</h4>
                <ul class="text-sm text-blue-800 space-y-1">
                    <li>• Удалены дублирующиеся функции из config/config.php</li>
                    <li>• <strong>ИСПРАВЛЕНО:</strong> Удалена дублирующаяся функция redirect() из config/config.php</li>
                    <li>• Исправлена структура таблицы investments в database/update_via_web.php</li>
                    <li>• Добавлен session_start() в test_system.php</li>
                    <li>• Все функции теперь определены только в includes/functions.php</li>
                    <li>• Исправлены ошибки "Cannot redeclare function"</li>
                    <li>• Функция redirect() работает корректно без дублирования</li>
                </ul>
            </div>
        </div>
    </div>
</body>
</html>
