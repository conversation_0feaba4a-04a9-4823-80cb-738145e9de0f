<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Проверка всех исправлений</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-3xl font-bold text-gray-900 mb-8'>✅ Проверка всех исправлений</h1>";

$all_passed = true;
$test_results = [];

// Test 1: Check admin files for Russian language
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>1. Проверка русификации админ-панели</h2>";

$admin_files = ['admin/blog-edit.php', 'admin/settings.php', 'admin/blog.php'];
$english_phrases = ['Invalid security token', 'Blog post created successfully', 'Failed to create', 'Excerpt', 'Content *', 'SEO Settings'];

foreach ($admin_files as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $found_english = false;
        
        foreach ($english_phrases as $phrase) {
            if (strpos($content, $phrase) !== false) {
                echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Найден английский текст '$phrase' в $file</p>";
                $found_english = true;
                $all_passed = false;
            }
        }
        
        if (!$found_english) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Файл $file полностью русифицирован</p>";
        }
    }
}

echo "</div>";

// Test 2: Check database tables
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>2. Проверка целостности базы данных</h2>";

try {
    $db = getDB();
    $required_tables = ['users', 'investments', 'user_investments', 'transactions', 'daily_profits', 'blog_posts', 'contact_messages', 'site_settings', 'balance_changes'];
    
    foreach ($required_tables as $table) {
        $stmt = $db->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        
        if ($stmt->fetch()) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Таблица '$table' существует</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Таблица '$table' отсутствует</p>";
            $all_passed = false;
        }
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка подключения к БД: " . $e->getMessage() . "</p>";
    $all_passed = false;
}

echo "</div>";

// Test 3: Check mobile navigation
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>3. Проверка мобильной навигации</h2>";

if (file_exists('includes/header.php')) {
    $header_content = file_get_contents('includes/header.php');
    
    if (strpos($header_content, 'bg-slate-900') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Мобильное меню имеет темный фон</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Мобильное меню не имеет темного фона</p>";
        $all_passed = false;
    }
    
    if (strpos($header_content, 'text-white') !== false) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Мобильное меню имеет белый текст</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Мобильное меню не имеет белого текста</p>";
        $all_passed = false;
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл includes/header.php не найден</p>";
    $all_passed = false;
}

echo "</div>";

// Test 4: Check transaction screenshot functionality
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>4. Проверка системы скриншотов транзакций</h2>";

if (file_exists('admin/transactions.php')) {
    $trans_content = file_get_contents('admin/transactions.php');
    $required_elements = ['viewScreenshot', 'screenshotModal', 'screenshot_path'];
    
    foreach ($required_elements as $element) {
        if (strpos($trans_content, $element) !== false) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Элемент '$element' найден</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Элемент '$element' не найден</p>";
            $all_passed = false;
        }
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл admin/transactions.php не найден</p>";
    $all_passed = false;
}

echo "</div>";

// Test 5: Check site settings integration
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>5. Проверка интеграции настроек сайта</h2>";

try {
    $test_setting = getSiteSetting('site_name', 'default');
    if ($test_setting !== null) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция getSiteSetting работает</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция getSiteSetting не работает</p>";
        $all_passed = false;
    }
    
    if (function_exists('updateSiteSetting')) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция updateSiteSetting существует</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция updateSiteSetting не найдена</p>";
        $all_passed = false;
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Ошибка проверки настроек: " . $e->getMessage() . "</p>";
    $all_passed = false;
}

echo "</div>";

// Test 6: Check upload directories
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>6. Проверка директорий загрузок</h2>";

$upload_dirs = ['uploads', 'uploads/transactions', 'uploads/blog'];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Директория '$dir' существует и доступна для записи</p>";
        } else {
            echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-2'></i>Директория '$dir' существует, но недоступна для записи</p>";
        }
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Директория '$dir' не существует</p>";
        $all_passed = false;
    }
}

echo "</div>";

// Test 7: Check legal pages
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>7. Проверка правовых страниц</h2>";

$legal_pages = [
    'pages/privacy-policy.php' => 'Политика конфиденциальности',
    'pages/terms-of-service.php' => 'Условия использования',
    'pages/risk-disclosure.php' => 'Раскрытие рисков',
    'pages/faq.php' => 'Часто задаваемые вопросы'
];

foreach ($legal_pages as $file => $name) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strlen($content) > 1000) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>$name ($file) создана</p>";
        } else {
            echo "<p class='text-yellow-600'><i class='fas fa-exclamation-triangle mr-2'></i>$name ($file) слишком короткая</p>";
        }
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>$name ($file) не найдена</p>";
        $all_passed = false;
    }
}

echo "</div>";

// Final result
if ($all_passed) {
    echo "<div class='bg-green-50 border border-green-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-green-900 mb-4'><i class='fas fa-check-circle mr-2'></i>Все исправления успешно применены!</h2>
            <p class='text-green-700 mb-4'>Поздравляем! Все проблемы, выявленные в test_improvements.php, были успешно исправлены.</p>
            <div class='grid grid-cols-1 md:grid-cols-3 gap-4'>
                <a href='test_improvements.php' class='bg-green-500 hover:bg-green-600 text-white px-4 py-3 rounded-lg text-center transition-colors'>
                    <i class='fas fa-vial mr-2'></i>Запустить полные тесты
                </a>
                <a href='admin/index.php' class='bg-blue-500 hover:bg-blue-600 text-white px-4 py-3 rounded-lg text-center transition-colors'>
                    <i class='fas fa-cog mr-2'></i>Админ-панель
                </a>
                <a href='index.php' class='bg-purple-500 hover:bg-purple-600 text-white px-4 py-3 rounded-lg text-center transition-colors'>
                    <i class='fas fa-home mr-2'></i>Главная страница
                </a>
            </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border border-red-200 rounded-lg p-6'>
            <h2 class='text-xl font-bold text-red-900 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Обнаружены проблемы</h2>
            <p class='text-red-700 mb-4'>Некоторые исправления требуют дополнительной настройки. Проверьте детали выше.</p>
            <div class='flex space-x-4'>
                <a href='fix_database_integrity.php' class='bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg'>
                    <i class='fas fa-wrench mr-2'></i>Исправить БД
                </a>
                <a href='test_improvements.php' class='bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-3 rounded-lg'>
                    <i class='fas fa-vial mr-2'></i>Полные тесты
                </a>
            </div>
          </div>";
}

echo "</div></body></html>";
?>
