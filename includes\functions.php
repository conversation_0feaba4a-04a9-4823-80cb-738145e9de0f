<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/config.php';

// User functions
function getUserById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch();
}

function getUserByEmail($email) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM users WHERE email = ?");
    $stmt->execute([$email]);
    return $stmt->fetch();
}

function createUser($username, $email, $password) {
    $db = getDB();
    // Use plain text password for testing as requested
    $stmt = $db->prepare("INSERT INTO users (username, email, password) VALUES (?, ?, ?)");
    return $stmt->execute([$username, $email, $password]);
}

function updateUserBalance($user_id, $amount, $operation = 'add') {
    $db = getDB();
    
    if ($operation === 'add') {
        $stmt = $db->prepare("UPDATE users SET balance = balance + ? WHERE id = ?");
    } else {
        $stmt = $db->prepare("UPDATE users SET balance = balance - ? WHERE id = ?");
    }
    
    return $stmt->execute([$amount, $user_id]);
}

// Mining Package functions
function getAllMiningPackages($active_only = true) {
    $db = getDB();
    $sql = "SELECT * FROM mining_packages";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY created_at DESC";

    $stmt = $db->prepare($sql);
    $stmt->execute();
    $packages = $stmt->fetchAll();

    // Add compatibility fields for each package
    foreach ($packages as &$package) {
        $package = addMiningPackageCompatibilityFields($package);
    }

    return $packages;
}

// Legacy function for backward compatibility
function getAllInvestments($active_only = true) {
    return getAllMiningPackages($active_only);
}

function getMiningPackageById($id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM mining_packages WHERE id = ?");
    $stmt->execute([$id]);
    $package = $stmt->fetch();

    // Add compatibility fields for old code
    if ($package) {
        $package = addMiningPackageCompatibilityFields($package);
    }

    return $package;
}

// Legacy function for backward compatibility
function getInvestmentById($id) {
    return getMiningPackageById($id);
}

// Add compatibility fields for mining packages to work with old frontend code
function addMiningPackageCompatibilityFields($package) {
    if (!$package) return $package;

    // Add missing fields with default values or computed values for backward compatibility
    $package['price'] = $package['max_amount'] ?? $package['min_amount'];
    $package['monthly_rate_min'] = $package['daily_rate'] * 30; // Convert daily to monthly
    $package['monthly_rate_max'] = $package['daily_rate'] * 30;
    $package['monthly_rate'] = $package['daily_rate'] * 30;
    $package['return_period_months'] = $package['duration_months'] ?? 12;

    // Ensure required fields exist with defaults
    if (!isset($package['location'])) {
        $package['location'] = ucfirst(str_replace('_', ' ', $package['category']));
    }
    if (!isset($package['capital_return'])) {
        $package['capital_return'] = true;
    }
    if (!isset($package['features'])) {
        $package['features'] = $package['hash_rate'] . ' | ' . $package['energy_source'];
    }

    return $package;
}

// Legacy function for backward compatibility
function addInvestmentCompatibilityFields($investment) {
    return addMiningPackageCompatibilityFields($investment);
}

function createUserMiningInvestment($user_id, $package_id, $amount, $daily_rate, $return_period_months = null) {
    $db = getDB();

    // Get mining package details to determine duration if not provided
    if ($return_period_months === null) {
        $package = getMiningPackageById($package_id);
        $return_period_months = $package['duration_months'] ?? 12;
    }

    $start_date = date('Y-m-d');
    $end_date = date('Y-m-d', strtotime("+{$return_period_months} months"));

    $stmt = $db->prepare("
        INSERT INTO user_mining_investments (user_id, package_id, amount, daily_rate, start_date, end_date)
        VALUES (?, ?, ?, ?, ?, ?)
    ");

    return $stmt->execute([$user_id, $package_id, $amount, $daily_rate, $start_date, $end_date]);
}

// Legacy function for backward compatibility
function createUserInvestment($user_id, $investment_id, $amount, $monthly_rate, $return_period_months = null) {
    $daily_rate = $monthly_rate / 30; // Convert monthly to daily rate
    return createUserMiningInvestment($user_id, $investment_id, $amount, $daily_rate, $return_period_months);
}

function getUserMiningInvestments($user_id) {
    $db = getDB();
    $stmt = $db->prepare("
        SELECT umi.*, mp.title, mp.category, mp.image_url, mp.hash_rate, mp.energy_source
        FROM user_mining_investments umi
        JOIN mining_packages mp ON umi.package_id = mp.id
        WHERE umi.user_id = ?
        ORDER BY umi.created_at DESC
    ");
    $stmt->execute([$user_id]);
    return $stmt->fetchAll();
}

// Legacy function for backward compatibility
function getUserInvestments($user_id) {
    return getUserMiningInvestments($user_id);
}

// Transaction functions
function createTransaction($user_id, $type, $amount, $screenshot_path = null, $wallet_address = null) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, screenshot_path, wallet_address, status)
        VALUES (?, ?, ?, ?, ?, 'pending')
    ");

    return $stmt->execute([$user_id, $type, $amount, $screenshot_path, $wallet_address]);
}

function getUserTransactions($user_id, $limit = 10) {
    $db = getDB();

    // Ensure limit is an integer and within reasonable bounds
    $limit = max(1, min(100, intval($limit)));

    // Get regular transactions and daily profits combined
    $stmt = $db->prepare("
        (SELECT
            id,
            'transaction' as source_type,
            type,
            amount,
            status,
            created_at,
            wallet_address,
            screenshot_path,
            admin_notes
        FROM transactions
        WHERE user_id = ?)
        UNION ALL
        (SELECT
            dmp.id,
            'mining_profit' as source_type,
            'mining_profit_credit' as type,
            dmp.profit_amount as amount,
            'completed' as status,
            dmp.created_at,
            NULL as wallet_address,
            NULL as screenshot_path,
            CONCAT('Daily mining profit from package #', umi.package_id) as admin_notes
        FROM daily_mining_profits dmp
        JOIN user_mining_investments umi ON dmp.user_mining_investment_id = umi.id
        WHERE umi.user_id = ?)
        ORDER BY created_at DESC
        LIMIT " . $limit
    );
    $stmt->execute([$user_id, $user_id]);
    return $stmt->fetchAll();
}

function getPendingTransactions($type = null) {
    $db = getDB();
    $sql = "
        SELECT t.*, u.username, u.email 
        FROM transactions t 
        JOIN users u ON t.user_id = u.id 
        WHERE t.status = 'pending'
    ";
    
    if ($type) {
        $sql .= " AND t.type = ?";
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute([$type]);
    } else {
        $stmt = $db->prepare($sql . " ORDER BY t.created_at ASC");
        $stmt->execute();
    }
    
    return $stmt->fetchAll();
}

function updateTransactionStatus($transaction_id, $status, $admin_notes = null) {
    $db = getDB();
    $stmt = $db->prepare("
        UPDATE transactions 
        SET status = ?, admin_notes = ?, processed_at = NOW() 
        WHERE id = ?
    ");
    
    return $stmt->execute([$status, $admin_notes, $transaction_id]);
}

// Daily mining profit calculation
function calculateDailyMiningProfit($user_mining_investment_id, $amount, $daily_rate) {
    return ($amount * $daily_rate) / 100;
}

function addDailyMiningProfit($user_mining_investment_id, $profit_amount, $date = null) {
    $db = getDB();

    if (!$date) {
        $date = date('Y-m-d');
    }

    $stmt = $db->prepare("
        INSERT IGNORE INTO daily_mining_profits (user_mining_investment_id, profit_amount, profit_date)
        VALUES (?, ?, ?)
    ");

    return $stmt->execute([$user_mining_investment_id, $profit_amount, $date]);
}

// Legacy functions for backward compatibility
function calculateDailyProfit($user_investment_id, $amount, $monthly_rate) {
    $daily_rate = $monthly_rate / 30;
    return calculateDailyMiningProfit($user_investment_id, $amount, $daily_rate);
}

function addDailyProfit($user_investment_id, $profit_amount, $date = null) {
    return addDailyMiningProfit($user_investment_id, $profit_amount, $date);
}

// Eco Mining News functions
function getEcoMiningNews($published_only = true, $limit = null) {
    $db = getDB();
    $sql = "SELECT * FROM news";

    if ($published_only) {
        $sql .= " WHERE is_published = 1";
    }

    $sql .= " ORDER BY created_at DESC";

    if ($limit) {
        $sql .= " LIMIT " . intval($limit);
    }

    $stmt = $db->prepare($sql);
    $stmt->execute();
    return $stmt->fetchAll();
}

// Legacy function for backward compatibility
function getBlogPosts($published_only = true, $limit = null) {
    return getEcoMiningNews($published_only, $limit);
}

function getEcoMiningNewsBySlug($slug) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM news WHERE slug = ? AND is_published = 1");
    $stmt->execute([$slug]);
    return $stmt->fetch();
}

// Legacy function for backward compatibility
function getBlogPostBySlug($slug) {
    return getEcoMiningNewsBySlug($slug);
}

// Contact functions
function saveContactMessage($name, $email, $subject, $message) {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO contact_messages (name, email, subject, message) 
        VALUES (?, ?, ?, ?)
    ");
    
    return $stmt->execute([$name, $email, $subject, $message]);
}

// Statistics functions
function getDashboardStats($user_id) {
    $db = getDB();

    // Get user balance
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    // Get total invested amount in mining
    $stmt = $db->prepare("SELECT SUM(amount) as total_invested FROM user_mining_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $total_invested = $stmt->fetch()['total_invested'] ?? 0;

    // Get total mining profit earned
    $stmt = $db->prepare("
        SELECT SUM(dmp.profit_amount) as total_profit
        FROM daily_mining_profits dmp
        JOIN user_mining_investments umi ON dmp.user_mining_investment_id = umi.id
        WHERE umi.user_id = ?
    ");
    $stmt->execute([$user_id]);
    $total_profit = $stmt->fetch()['total_profit'] ?? 0;

    // Get active mining investments count
    $stmt = $db->prepare("SELECT COUNT(*) as active_investments FROM user_mining_investments WHERE user_id = ? AND is_active = 1");
    $stmt->execute([$user_id]);
    $active_investments = $stmt->fetch()['active_investments'] ?? 0;

    return [
        'balance' => floatval($balance),
        'total_invested' => floatval($total_invested),
        'total_profit' => floatval($total_profit),
        'active_investments' => intval($active_investments)
    ];
}

// Withdrawal processing function

function processWithdrawalRequest($user_id, $amount, $wallet_address) {
    $user = getUserById($user_id);
    $balance = $user['balance'] ?? 0;

    if ($amount < 10) {
        return ['success' => false, 'message' => 'Minimum withdrawal amount is $10'];
    }

    if ($amount > $balance) {
        return ['success' => false, 'message' => 'Insufficient balance'];
    }

    if (empty($wallet_address)) {
        return ['success' => false, 'message' => 'Wallet address is required'];
    }

    // Create withdrawal transaction directly with new schema
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO transactions (user_id, type, amount, wallet_address, status)
        VALUES (?, 'withdrawal', ?, ?, 'pending')
    ");

    if ($stmt->execute([$user_id, $amount, $wallet_address])) {
        return ['success' => true, 'message' => 'Withdrawal request submitted successfully. It will be processed within 24-48 hours.'];
    } else {
        return ['success' => false, 'message' => 'Failed to submit withdrawal request. Please try again.'];
    }
}

// Formatting functions
function formatCurrency($amount) {
    return '$' . number_format(floatval($amount), 2);
}

function formatPercentage($percentage) {
    return number_format(floatval($percentage), 2);
}

// CSRF Protection functions
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

// Verification functions
function getUserVerificationStatus($user_id) {
    $user = getUserById($user_id);
    return $user['verification_status'] ?? 'unverified';
}

function isUserVerified($user_id) {
    return getUserVerificationStatus($user_id) === 'verified';
}

function getUserVerification($user_id) {
    $db = getDB();
    $stmt = $db->prepare("SELECT * FROM user_verifications WHERE user_id = ? ORDER BY submitted_at DESC LIMIT 1");
    $stmt->execute([$user_id]);
    return $stmt->fetch();
}

function submitVerificationRequest($user_id, $first_name, $last_name, $birth_date, $passport_file) {
    // Upload passport photo
    $uploadResult = uploadPassportPhoto($passport_file);

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    $db = getDB();

    try {
        $db->beginTransaction();

        // Insert verification request
        $stmt = $db->prepare("
            INSERT INTO user_verifications (user_id, first_name, last_name, birth_date, passport_photo_path, status)
            VALUES (?, ?, ?, ?, ?, 'pending')
        ");

        if (!$stmt->execute([$user_id, $first_name, $last_name, $birth_date, $uploadResult['file_path']])) {
            throw new Exception('Failed to insert verification request');
        }

        // Update user verification status
        $stmt = $db->prepare("UPDATE users SET verification_status = 'pending' WHERE id = ?");
        if (!$stmt->execute([$user_id])) {
            throw new Exception('Failed to update user verification status');
        }

        $db->commit();

        return ['success' => true, 'message' => 'Verification request submitted successfully. Please wait for admin approval.'];

    } catch (Exception $e) {
        $db->rollback();

        // Delete uploaded file if database insert fails
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
        }

        logError("Verification submission error: " . $e->getMessage());
        return ['success' => false, 'message' => 'Failed to submit verification request. Please try again.'];
    }
}

function uploadPassportPhoto($file) {
    $uploadDir = 'uploads/verifications/';

    // Create directory if it doesn't exist
    if (!is_dir($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
    }

    // Validate file
    $allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!in_array($file['type'], $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type. Please upload JPG, PNG, or GIF image.'];
    }

    if ($file['size'] > 5 * 1024 * 1024) { // 5MB limit
        return ['success' => false, 'message' => 'File size too large. Maximum size is 5MB.'];
    }

    // Generate unique filename
    $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
    $filename = 'passport_' . uniqid() . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $fullPath = __DIR__ . '/../' . $filepath;

    if (move_uploaded_file($file['tmp_name'], $fullPath)) {
        return [
            'success' => true,
            'file_path' => $filepath,
            'full_path' => $fullPath
        ];
    } else {
        return ['success' => false, 'message' => 'Failed to upload file'];
    }
}

function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

// Flash message functions
function setFlashMessage($type, $message) {
    $_SESSION['flash_messages'][$type] = $message;
}

function getFlashMessage($type) {
    if (isset($_SESSION['flash_messages'][$type])) {
        $message = $_SESSION['flash_messages'][$type];
        unset($_SESSION['flash_messages'][$type]);
        return $message;
    }
    return null;
}

function displayFlashMessages() {
    $success_message = getFlashMessage('success');
    $error_message = getFlashMessage('error');
    $info_message = getFlashMessage('info');

    if ($success_message): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success_message); ?>
        </div>
    <?php endif;

    if ($error_message): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif;

    if ($info_message): ?>
        <div class="mb-6 bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg animate-fade-in">
            <i class="fas fa-info-circle mr-2"></i><?php echo htmlspecialchars($info_message); ?>
        </div>
    <?php endif;
}

// Redirect function
function redirect($url) {
    header("Location: $url");
    exit();
}

// Eco Mining News image functions
function getEcoMiningNewsImage($news, $size = 'medium') {
    // Default fallback image for eco mining news
    $default_image = 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80';

    // Check if news has image_url key and it's not empty
    if (isset($news['image_url']) && !empty($news['image_url'])) {
        return $news['image_url'];
    }

    return $default_image;
}

function getEscapedEcoMiningNewsImage($news, $size = 'medium') {
    return htmlspecialchars(getEcoMiningNewsImage($news, $size));
}

// Legacy functions for backward compatibility
function getBlogPostImage($post, $size = 'medium') {
    return getEcoMiningNewsImage($post, $size);
}

function getEscapedBlogImage($post, $size = 'medium') {
    return getEscapedEcoMiningNewsImage($post, $size);
}

// Mining Package image functions
function getMiningPackageImage($package, $size = 'medium') {
    // Default fallback image for mining packages based on category
    $category_images = [
        'solar_mining' => 'https://images.unsplash.com/photo-1509391366360-2e959784a276?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
        'wind_mining' => 'https://images.unsplash.com/photo-1532601224476-15c79f2f7a51?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
        'hydro_mining' => 'https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
        'geothermal_mining' => 'https://images.unsplash.com/photo-1473341304170-971dccb5ac1e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
        'green_hosting' => 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80',
        'carbon_neutral' => 'https://images.unsplash.com/photo-1441974231531-c6227db76b6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80'
    ];

    $default_image = $category_images['solar_mining']; // Default to solar

    // Check if package has image_url key and it's not empty
    if (isset($package['image_url']) && !empty($package['image_url'])) {
        return $package['image_url'];
    }

    // Use category-specific image if available
    if (isset($package['category']) && isset($category_images[$package['category']])) {
        return $category_images[$package['category']];
    }

    return $default_image;
}

function getEscapedMiningPackageImage($package, $size = 'medium') {
    return htmlspecialchars(getMiningPackageImage($package, $size));
}

// Legacy functions for backward compatibility
function getInvestmentImage($investment, $size = 'medium') {
    return getMiningPackageImage($investment, $size);
}

function getEscapedInvestmentImage($investment, $size = 'medium') {
    return getEscapedMiningPackageImage($investment, $size);
}

// User status functions
function getUserStatus($user) {
    // Check for new status field first, then fallback to legacy is_suspended
    if (isset($user['status'])) {
        return $user['status'];
    } elseif (isset($user['is_suspended']) && $user['is_suspended']) {
        return 'suspended';
    } else {
        return 'active';
    }
}

function getUserRole($user) {
    // Check for new role field first, then fallback to legacy is_admin
    if (isset($user['role'])) {
        return $user['role'];
    } elseif (isset($user['is_admin']) && $user['is_admin']) {
        return 'admin';
    } else {
        return 'user';
    }
}

function isUserSuspended($user) {
    return getUserStatus($user) === 'suspended';
}

function isUserAdmin($user) {
    return getUserRole($user) === 'admin';
}

// File upload functions
function uploadTransactionScreenshot($file) {
    // Use absolute path from document root
    $uploadDir = __DIR__ . '/../uploads/transactions/';
    $webPath = 'uploads/transactions/';
    $allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/jpg'];
    $maxSize = 5 * 1024 * 1024; // 5MB

    // Enhanced logging for debugging
    error_log("=== uploadTransactionScreenshot DEBUG ===");
    error_log("Upload dir: $uploadDir");
    error_log("File data: " . print_r($file, true));

    // Validate file
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        error_log("No tmp_name in file array");
        return ['success' => false, 'message' => 'No file uploaded - missing tmp_name'];
    }

    if (!is_uploaded_file($file['tmp_name'])) {
        error_log("File is not an uploaded file: " . $file['tmp_name']);
        return ['success' => false, 'message' => 'Invalid uploaded file'];
    }

    if ($file['error'] !== UPLOAD_ERR_OK) {
        error_log("Upload error: " . $file['error']);
        return ['success' => false, 'message' => 'File upload error: ' . $file['error']];
    }

    if ($file['size'] > $maxSize) {
        error_log("File too large: " . $file['size']);
        return ['success' => false, 'message' => 'File size exceeds 5MB limit'];
    }

    if (!in_array($file['type'], $allowedTypes)) {
        error_log("Invalid file type: " . $file['type']);
        return ['success' => false, 'message' => 'Invalid file type. Only JPG, PNG, and GIF are allowed'];
    }

    // Create directory if it doesn't exist
    if (!file_exists($uploadDir)) {
        if (!mkdir($uploadDir, 0755, true)) {
            error_log("Failed to create upload directory: $uploadDir");
            return ['success' => false, 'message' => 'Failed to create upload directory'];
        }
        error_log("Created upload directory: $uploadDir");
    }

    // Generate unique filename
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    $filename = uniqid('transaction_') . '.' . $extension;
    $filepath = $uploadDir . $filename;
    $webFilepath = $webPath . $filename;

    error_log("Attempting to move file from {$file['tmp_name']} to $filepath");

    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        error_log("File uploaded successfully to: $filepath");
        return ['success' => true, 'filepath' => $webFilepath, 'full_path' => $filepath];
    } else {
        error_log("Failed to move uploaded file");
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }
}

// Process deposit request with screenshot
function processDepositRequest($user_id, $amount, $screenshot_file) {
    error_log("=== processDepositRequest DEBUG ===");
    error_log("User ID: $user_id");
    error_log("Amount: $amount");
    error_log("Screenshot file: " . print_r($screenshot_file, true));

    if ($amount < 10) {
        error_log("Amount too small: $amount");
        return ['success' => false, 'message' => 'Minimum deposit amount is $10'];
    }

    // Upload screenshot
    $uploadResult = uploadTransactionScreenshot($screenshot_file);
    error_log("Upload result: " . print_r($uploadResult, true));

    if (!$uploadResult['success']) {
        return $uploadResult;
    }

    try {
        $db = getDB();
        $stmt = $db->prepare("
            INSERT INTO transactions (user_id, type, amount, screenshot_path, status, created_at)
            VALUES (?, 'deposit', ?, ?, 'pending', NOW())
        ");

        $filepath = $uploadResult['filepath'];
        error_log("Inserting transaction with filepath: $filepath");

        if ($stmt->execute([$user_id, $amount, $filepath])) {
            $transaction_id = $db->lastInsertId();
            error_log("Transaction inserted successfully with ID: $transaction_id");
            return ['success' => true, 'message' => 'Deposit request submitted successfully. Please wait for admin approval.', 'transaction_id' => $transaction_id];
        } else {
            error_log("Failed to insert transaction");
            error_log("SQL Error: " . print_r($stmt->errorInfo(), true));

            // Delete uploaded file if database insert fails
            $fullPath = $uploadResult['full_path'] ?? null;
            if ($fullPath && file_exists($fullPath)) {
                unlink($fullPath);
                error_log("Deleted uploaded file due to DB error: $fullPath");
            }
            return ['success' => false, 'message' => 'Failed to submit deposit request. Please try again.'];
        }
    } catch (Exception $e) {
        error_log("Exception in processDepositRequest: " . $e->getMessage());

        // Delete uploaded file if exception occurs
        $fullPath = $uploadResult['full_path'] ?? null;
        if ($fullPath && file_exists($fullPath)) {
            unlink($fullPath);
            error_log("Deleted uploaded file due to exception: $fullPath");
        }
        return ['success' => false, 'message' => 'Database error: ' . $e->getMessage()];
    }
}

// Site settings functions
function getSiteSetting($key, $default = '') {
    $db = getDB();
    $stmt = $db->prepare("SELECT setting_value FROM site_settings WHERE setting_key = ?");
    $stmt->execute([$key]);
    $result = $stmt->fetch();
    return $result ? $result['setting_value'] : $default;
}

function updateSiteSetting($key, $value, $type = 'text') {
    $db = getDB();
    $stmt = $db->prepare("
        INSERT INTO site_settings (setting_key, setting_value, setting_type)
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE setting_value = ?, updated_at = CURRENT_TIMESTAMP
    ");
    return $stmt->execute([$key, $value, $type, $value]);
}

function getAllSiteSettings() {
    $db = getDB();
    $stmt = $db->query("SELECT * FROM site_settings ORDER BY setting_key");
    $settings = [];
    while ($row = $stmt->fetch()) {
        $settings[$row['setting_key']] = $row['setting_value'];
    }
    return $settings;
}
?>
