# 🌱 EcoGenix - Установка

Руководство по установке платформы экологического майнинга криптовалют EcoGenix.

## 📋 Системные требования

### Обязательные требования:
- **PHP 7.4+** с поддержкой PDO
- **MySQL 5.7+** или **MariaDB 10.2+**
- **PDO MySQL** расширение
- **JSON** расширение
- **mbstring** расширение
- **OpenSSL** расширение

### Рекомендуемые расширения:
- **cURL** - для внешних API запросов
- **GD** - для обработки изображений

## 🚀 Быстрая установка

### Шаг 1: Проверка требований
```
http://ваш-домен/check_requirements.php
```

### Шаг 2: Автоматическая установка
```
http://ваш-домен/install.php
```

### Шаг 3: Настройка базы данных
1. Введите данные подключения к MySQL
2. Укажите название базы данных (по умолчанию: `ecogenix_mining`)
3. Нажмите "Установить EcoGenix"

## 📁 Структура проекта

```
ecogenix/
├── config/
│   ├── config.php          # Основная конфигурация
│   └── database.php        # Настройки БД (создается установщиком)
├── database/
│   └── schema.sql          # SQL схема и тестовые данные
├── includes/
│   ├── header.php          # Заголовок с CSS
│   ├── footer.php          # Подвал
│   └── functions.php       # Основные функции
├── pages/                  # Страницы приложения
├── admin/                  # Админ-панель
├── assets/                 # Статические файлы
├── install.php             # Установщик
├── check_requirements.php  # Проверка требований
└── index.php              # Главная страница
```

## 🔧 Ручная установка

Если автоматическая установка не работает:

### 1. Создание базы данных
```sql
CREATE DATABASE ecogenix_mining CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE ecogenix_mining;
```

### 2. Импорт схемы
```bash
mysql -u username -p ecogenix_mining < database/schema.sql
```

### 3. Настройка конфигурации
Отредактируйте `config/database.php`:
```php
<?php
return [
    'host' => 'localhost',
    'dbname' => 'ecogenix_mining',
    'username' => 'your_username',
    'password' => 'your_password',
    'charset' => 'utf8mb4'
];
?>
```

## 🌱 Что включено в установку

### База данных:
- **6 майнинг-пакетов** с разными типами энергии
- **3 новостные статьи** об экологическом майнинге
- **Пользователи и роли**
- **Система транзакций**
- **Ежедневные прибыли от майнинга**

### Тестовые майнинг-пакеты:
1. **Solar Genesis** - солнечная энергия (1.2%/день)
2. **Wind Harvester** - ветровая энергия (1.5%/день)
3. **Hydro Power** - гидроэнергия (1.8%/день)
4. **Geothermal Pro** - геотермальная энергия (2.1%/день)
5. **Green Hosting** - зеленый хостинг (0.8%/день)
6. **Carbon Neutral** - углеродно-нейтральный (2.5%/день)

## 🔐 Безопасность

### Настройки для продакшена:
1. Измените пароли по умолчанию
2. Включите HTTPS
3. Настройте файрвол
4. Обновите `config/config.php`:
   ```php
   'environment' => 'production',
   'debug' => false,
   ```

### Права доступа:
```bash
chmod 755 config/
chmod 644 config/*.php
chmod 755 assets/
```

## 🐛 Устранение проблем

### Ошибка подключения к БД:
- Проверьте данные подключения
- Убедитесь, что MySQL запущен
- Проверьте права пользователя

### Ошибки PHP:
- Проверьте версию PHP (должна быть 7.4+)
- Убедитесь, что все расширения установлены
- Проверьте логи веб-сервера

### Проблемы с правами:
```bash
chown -R www-data:www-data /path/to/ecogenix
chmod -R 755 /path/to/ecogenix
```

## 📞 Поддержка

После установки доступны:
- **Главная страница**: `http://ваш-домен/`
- **Регистрация**: `http://ваш-домен/pages/register.php`
- **Админ-панель**: `http://ваш-домен/admin/`

## 🌍 Экологические особенности

EcoGenix включает:
- ✅ 100% возобновляемая энергия
- ✅ Углеродно-нейтральные операции
- ✅ Отслеживание экологического воздействия
- ✅ Ежедневные прибыли от экологического майнинга
- ✅ Современный зеленый дизайн

---

**🌱 EcoGenix** - Будущее экологического майнинга криптовалют!
