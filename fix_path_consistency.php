<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Исправление путей файлов</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-6xl mx-auto p-8'>
    <h1 class='text-4xl font-bold text-gray-900 mb-8'>🔧 Исправление путей файлов</h1>";

$fixes_applied = [];
$errors = [];

// Step 1: Ensure standard upload directory exists
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📁 Шаг 1: Создание стандартной директории</h2>";

$standard_upload_dir = 'uploads/transactions/';

if (!is_dir($standard_upload_dir)) {
    if (mkdir($standard_upload_dir, 0755, true)) {
        echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Создана директория: $standard_upload_dir</p>";
        $fixes_applied[] = "Создана директория $standard_upload_dir";
    } else {
        echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Ошибка создания директории: $standard_upload_dir</p>";
        $errors[] = "Не удалось создать директорию $standard_upload_dir";
    }
} else {
    echo "<p class='text-blue-600 mb-2'><i class='fas fa-info mr-2'></i>Директория уже существует: $standard_upload_dir</p>";
}

// Check if writable
if (is_writable($standard_upload_dir)) {
    echo "<p class='text-green-600 mb-2'><i class='fas fa-check mr-2'></i>Директория доступна для записи</p>";
} else {
    echo "<p class='text-red-600 mb-2'><i class='fas fa-times mr-2'></i>Директория НЕ доступна для записи</p>";
    $errors[] = "Директория $standard_upload_dir не доступна для записи";
}

echo "</div>";

// Step 2: Check and move files from other directories
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>📦 Шаг 2: Перемещение файлов в стандартную директорию</h2>";

$other_dirs = ['admin/uploads/transactions/', '../uploads/transactions/'];
$moved_files = 0;

foreach ($other_dirs as $dir) {
    if (is_dir($dir)) {
        echo "<h3 class='font-bold text-gray-700 mb-2'>Проверка директории: $dir</h3>";
        
        $files = glob($dir . '*');
        if (!empty($files)) {
            foreach ($files as $file) {
                if (is_file($file)) {
                    $filename = basename($file);
                    $destination = $standard_upload_dir . $filename;
                    
                    // Check if destination already exists
                    if (file_exists($destination)) {
                        echo "<p class='text-yellow-600 text-sm'><i class='fas fa-exclamation-triangle mr-1'></i>Файл уже существует: $filename</p>";
                    } else {
                        if (copy($file, $destination)) {
                            echo "<p class='text-green-600 text-sm'><i class='fas fa-check mr-1'></i>Перемещен: $filename</p>";
                            unlink($file); // Remove original
                            $moved_files++;
                            $fixes_applied[] = "Перемещен файл $filename из $dir";
                        } else {
                            echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Ошибка перемещения: $filename</p>";
                            $errors[] = "Не удалось переместить $filename из $dir";
                        }
                    }
                }
            }
        } else {
            echo "<p class='text-gray-500 text-sm'>Нет файлов в $dir</p>";
        }
    }
}

if ($moved_files > 0) {
    echo "<p class='text-green-600 font-bold mt-4'><i class='fas fa-check mr-2'></i>Перемещено файлов: $moved_files</p>";
} else {
    echo "<p class='text-blue-600 font-bold mt-4'><i class='fas fa-info mr-2'></i>Нет файлов для перемещения</p>";
}

echo "</div>";

// Step 3: Update database paths
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>💾 Шаг 3: Обновление путей в базе данных</h2>";

try {
    $db = getDB();
    
    // Find transactions with non-standard paths
    $stmt = $db->query("SELECT id, screenshot_path FROM transactions WHERE screenshot_path IS NOT NULL AND screenshot_path != '' AND screenshot_path NOT LIKE 'uploads/transactions/%'");
    $transactions_to_update = $stmt->fetchAll();
    
    if (empty($transactions_to_update)) {
        echo "<p class='text-blue-600'><i class='fas fa-info mr-2'></i>Все пути в базе данных уже корректны</p>";
    } else {
        echo "<p class='text-yellow-600 mb-4'><i class='fas fa-exclamation-triangle mr-2'></i>Найдено " . count($transactions_to_update) . " записей с некорректными путями</p>";
        
        $updated_count = 0;
        foreach ($transactions_to_update as $transaction) {
            $old_path = $transaction['screenshot_path'];
            $filename = basename($old_path);
            $new_path = $standard_upload_dir . $filename;
            
            // Check if file exists in new location
            if (file_exists($new_path)) {
                $stmt = $db->prepare("UPDATE transactions SET screenshot_path = ? WHERE id = ?");
                if ($stmt->execute([$new_path, $transaction['id']])) {
                    echo "<p class='text-green-600 text-sm'><i class='fas fa-check mr-1'></i>Обновлен путь для ID {$transaction['id']}: $filename</p>";
                    $updated_count++;
                    $fixes_applied[] = "Обновлен путь в БД для транзакции {$transaction['id']}";
                } else {
                    echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Ошибка обновления ID {$transaction['id']}</p>";
                    $errors[] = "Не удалось обновить путь для транзакции {$transaction['id']}";
                }
            } else {
                echo "<p class='text-red-600 text-sm'><i class='fas fa-times mr-1'></i>Файл не найден для ID {$transaction['id']}: $filename</p>";
                $errors[] = "Файл не найден для транзакции {$transaction['id']}: $filename";
            }
        }
        
        if ($updated_count > 0) {
            echo "<p class='text-green-600 font-bold mt-4'><i class='fas fa-check mr-2'></i>Обновлено записей в БД: $updated_count</p>";
        }
    }
} catch (Exception $e) {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ошибка базы данных: " . $e->getMessage() . "</p>";
    $errors[] = "Ошибка базы данных: " . $e->getMessage();
}

echo "</div>";

// Step 4: Verify admin panel path compatibility
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🔍 Шаг 4: Проверка совместимости с админ-панелью</h2>";

// Check if admin panel can access files from root directory
$test_file_path = $standard_upload_dir . 'test_access.txt';
file_put_contents($test_file_path, 'Test file for path verification');

// Simulate admin panel access (from admin/ subdirectory)
$admin_relative_path = '../' . $standard_upload_dir . 'test_access.txt';

if (file_exists($admin_relative_path)) {
    echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Админ-панель может получить доступ к файлам через относительный путь</p>";
    $fixes_applied[] = "Подтверждена совместимость путей с админ-панелью";
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Админ-панель НЕ может получить доступ к файлам</p>";
    $errors[] = "Проблема доступа админ-панели к файлам";
}

// Clean up test file
unlink($test_file_path);

echo "</div>";

// Step 5: Test upload and display
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-2xl font-bold text-gray-900 mb-4'>🧪 Шаг 5: Тест загрузки и отображения</h2>";

// Test upload function
if (function_exists('uploadTransactionScreenshot')) {
    // Create a test image
    $test_image_data = base64_decode('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
    $test_file_path = 'test_path_fix.png';
    file_put_contents($test_file_path, $test_image_data);
    
    $test_file = [
        'name' => 'test_path_fix.png',
        'type' => 'image/png',
        'size' => strlen($test_image_data),
        'tmp_name' => $test_file_path,
        'error' => 0
    ];
    
    $upload_result = uploadTransactionScreenshot($test_file);
    
    if ($upload_result['success']) {
        $uploaded_file_path = $upload_result['filepath'];
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Тест загрузки успешен: $uploaded_file_path</p>";
        
        // Check if file is accessible from admin perspective
        $admin_view_path = '../' . $uploaded_file_path;
        if (file_exists($admin_view_path)) {
            echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Файл доступен из админ-панели</p>";
            $fixes_applied[] = "Подтвержден полный цикл загрузки и отображения";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Файл НЕ доступен из админ-панели</p>";
            $errors[] = "Проблема доступа к загруженному файлу из админ-панели";
        }
        
        // Clean up test file
        if (file_exists($uploaded_file_path)) {
            unlink($uploaded_file_path);
        }
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Ошибка тест загрузки: " . $upload_result['message'] . "</p>";
        $errors[] = "Ошибка тест загрузки: " . $upload_result['message'];
    }
    
    // Clean up original test file
    if (file_exists($test_file_path)) {
        unlink($test_file_path);
    }
} else {
    echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция uploadTransactionScreenshot не найдена</p>";
    $errors[] = "Функция uploadTransactionScreenshot не найдена";
}

echo "</div>";

// Final Results
if (empty($errors)) {
    echo "<div class='bg-green-50 border-2 border-green-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-green-500 mb-4'>
                <i class='fas fa-check-circle'></i>
            </div>
            <h2 class='text-3xl font-bold text-green-900 mb-4'>✅ ПУТИ ИСПРАВЛЕНЫ УСПЕШНО!</h2>
            <p class='text-green-700 text-lg mb-6'>Все файлы теперь используют единую структуру путей</p>";
    
    if (!empty($fixes_applied)) {
        echo "<div class='bg-white rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold text-green-900 mb-3'>Примененные исправления:</h3>
                <ul class='text-green-700 text-left space-y-1'>";
        foreach ($fixes_applied as $fix) {
            echo "<li>• $fix</li>";
        }
        echo "</ul>
              </div>";
    }
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='pages/dashboard.php' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Тест загрузки
            </a>
            <a href='admin/transactions.php' class='bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-eye mr-2'></i>Тест просмотра
            </a>
            <a href='diagnose_paths.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-search mr-2'></i>Диагностика
            </a>
          </div>
          </div>";
} else {
    echo "<div class='bg-red-50 border-2 border-red-200 rounded-lg p-8 text-center'>
            <div class='text-6xl text-red-500 mb-4'>
                <i class='fas fa-exclamation-triangle'></i>
            </div>
            <h2 class='text-3xl font-bold text-red-900 mb-4'>❌ Обнаружены проблемы</h2>
            <p class='text-red-700 text-lg mb-6'>Некоторые исправления не удалось применить</p>";
    
    echo "<div class='bg-white rounded-lg p-6 mb-6'>
            <h3 class='text-xl font-bold text-red-900 mb-3'>Ошибки:</h3>
            <ul class='text-red-700 text-left space-y-1'>";
    foreach ($errors as $error) {
        echo "<li>• $error</li>";
    }
    echo "</ul>
          </div>";
    
    if (!empty($fixes_applied)) {
        echo "<div class='bg-white rounded-lg p-6 mb-6'>
                <h3 class='text-xl font-bold text-green-900 mb-3'>Успешные исправления:</h3>
                <ul class='text-green-700 text-left space-y-1'>";
        foreach ($fixes_applied as $fix) {
            echo "<li>• $fix</li>";
        }
        echo "</ul>
              </div>";
    }
    
    echo "<div class='flex flex-wrap justify-center gap-4'>
            <a href='diagnose_paths.php' class='bg-orange-500 hover:bg-orange-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-search mr-2'></i>Повторная диагностика
            </a>
            <a href='manual_path_fix.php' class='bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg transition-colors'>
                <i class='fas fa-wrench mr-2'></i>Ручное исправление
            </a>
          </div>
          </div>";
}

echo "</div></body></html>";
?>
