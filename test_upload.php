<?php
require_once 'config/database.php';
require_once 'includes/functions.php';

echo "<!DOCTYPE html>
<html lang='ru'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Тест загрузки файлов</title>
    <script src='https://cdn.tailwindcss.com'></script>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
</head>
<body class='bg-gray-100'>
<div class='max-w-4xl mx-auto p-8'>
    <h1 class='text-3xl font-bold text-gray-900 mb-8'>🧪 Тест системы загрузки скриншотов</h1>";

// Handle file upload test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
            <h2 class='text-xl font-bold text-gray-900 mb-4'>Результат загрузки</h2>";
    
    $file = $_FILES['test_file'];
    
    echo "<div class='mb-4'>
            <h3 class='font-bold text-gray-700'>Информация о файле:</h3>
            <ul class='list-disc list-inside text-gray-600 mt-2'>
                <li>Имя: " . htmlspecialchars($file['name']) . "</li>
                <li>Размер: " . number_format($file['size'] / 1024, 2) . " KB</li>
                <li>Тип: " . htmlspecialchars($file['type']) . "</li>
                <li>Временный файл: " . htmlspecialchars($file['tmp_name']) . "</li>
                <li>Ошибка: " . $file['error'] . "</li>
            </ul>
          </div>";
    
    // Test upload function
    $result = uploadTransactionScreenshot($file);
    
    if ($result['success']) {
        echo "<div class='bg-green-50 border border-green-200 rounded-lg p-4 mb-4'>
                <h4 class='font-bold text-green-900'><i class='fas fa-check mr-2'></i>Загрузка успешна!</h4>
                <p class='text-green-700'>Файл сохранен: " . htmlspecialchars($result['filepath']) . "</p>";
        
        if (file_exists($result['filepath'])) {
            echo "<p class='text-green-700'>Файл существует на сервере ✅</p>";
            echo "<img src='" . htmlspecialchars($result['filepath']) . "' alt='Uploaded image' class='mt-4 max-w-md rounded-lg shadow-lg'>";
        } else {
            echo "<p class='text-red-700'>Файл НЕ найден на сервере ❌</p>";
        }
        
        echo "</div>";
    } else {
        echo "<div class='bg-red-50 border border-red-200 rounded-lg p-4'>
                <h4 class='font-bold text-red-900'><i class='fas fa-times mr-2'></i>Ошибка загрузки</h4>
                <p class='text-red-700'>" . htmlspecialchars($result['message']) . "</p>
              </div>";
    }
    
    echo "</div>";
}

// Display upload form
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Тест загрузки файла</h2>
        <form method='POST' enctype='multipart/form-data' class='space-y-4'>
            <div>
                <label class='block text-sm font-medium text-gray-700 mb-2'>Выберите изображение для тестирования</label>
                <input type='file' name='test_file' accept='image/*' required
                       class='block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100'>
            </div>
            <button type='submit' class='bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors'>
                <i class='fas fa-upload mr-2'></i>Загрузить тестовый файл
            </button>
        </form>
      </div>";

// Check directory permissions
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка директорий</h2>";

$upload_dirs = ['uploads', 'uploads/transactions'];

foreach ($upload_dirs as $dir) {
    if (is_dir($dir)) {
        $writable = is_writable($dir);
        $color = $writable ? 'green' : 'red';
        $icon = $writable ? 'check' : 'times';
        $status = $writable ? 'доступна для записи' : 'НЕ доступна для записи';
        
        echo "<p class='text-{$color}-600'><i class='fas fa-{$icon} mr-2'></i>Директория '$dir' существует и $status</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Директория '$dir' НЕ существует</p>";
        
        // Try to create directory
        if (mkdir($dir, 0755, true)) {
            echo "<p class='text-green-600'><i class='fas fa-plus mr-2'></i>Директория '$dir' создана</p>";
        } else {
            echo "<p class='text-red-600'><i class='fas fa-exclamation-triangle mr-2'></i>Не удалось создать директорию '$dir'</p>";
        }
    }
}

echo "</div>";

// Check functions
echo "<div class='bg-white rounded-lg shadow-lg p-6 mb-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Проверка функций</h2>";

$functions = ['uploadTransactionScreenshot', 'processDepositRequest'];

foreach ($functions as $func) {
    if (function_exists($func)) {
        echo "<p class='text-green-600'><i class='fas fa-check mr-2'></i>Функция '$func' существует</p>";
    } else {
        echo "<p class='text-red-600'><i class='fas fa-times mr-2'></i>Функция '$func' НЕ найдена</p>";
    }
}

echo "</div>";

// List existing uploaded files
echo "<div class='bg-white rounded-lg shadow-lg p-6'>
        <h2 class='text-xl font-bold text-gray-900 mb-4'>Существующие загруженные файлы</h2>";

if (is_dir('uploads/transactions')) {
    $files = glob('uploads/transactions/*');
    
    if (empty($files)) {
        echo "<p class='text-gray-500'>Нет загруженных файлов</p>";
    } else {
        echo "<div class='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4'>";
        foreach ($files as $file) {
            $filename = basename($file);
            $filesize = number_format(filesize($file) / 1024, 2);
            
            echo "<div class='border rounded-lg p-4'>
                    <img src='$file' alt='$filename' class='w-full h-32 object-cover rounded mb-2'>
                    <p class='text-sm font-medium text-gray-900'>$filename</p>
                    <p class='text-xs text-gray-500'>$filesize KB</p>
                  </div>";
        }
        echo "</div>";
    }
} else {
    echo "<p class='text-red-500'>Директория uploads/transactions не существует</p>";
}

echo "</div>";

echo "<div class='mt-8 text-center'>
        <a href='test_improvements.php' class='bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg transition-colors'>
            <i class='fas fa-arrow-left mr-2'></i>Вернуться к тестам
        </a>
      </div>";

echo "</div></body></html>";
?>
